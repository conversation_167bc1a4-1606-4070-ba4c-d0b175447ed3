<!--
  热更新管理页面
  对等 CLI 所有热更新功能的完整实现
-->

<template>
  <div class="hot-update-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">热更新管理</h1>
          <p class="page-subtitle">管理游戏资源的热更新，支持增量更新和版本回滚</p>
        </div>
        <div class="header-actions">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="btn btn-secondary"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
            刷新
          </button>
          <button
            @click="showGenerateManifestModal = true"
            class="btn btn-primary"
          >
            <DocumentPlusIcon class="w-4 h-4 mr-2" />
            生成资源清单
          </button>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">快速操作</h2>
        </div>
        <div class="card-content">
          <div class="actions-grid">
            <div class="action-group">
              <h3 class="action-group-title">资源管理</h3>
              <div class="action-buttons">
                <button
                  @click="showGenerateManifestModal = true"
                  class="action-button"
                >
                  <DocumentTextIcon class="w-5 h-5 mb-2" />
                  <span class="action-label">生成资源清单</span>
                  <span class="action-description">生成当前版本的资源清单</span>
                </button>
                <button
                  @click="showCheckChangesModal = true"
                  class="action-button"
                >
                  <MagnifyingGlassIcon class="w-5 h-5 mb-2" />
                  <span class="action-label">检查资源变更</span>
                  <span class="action-description">检查版本间的资源变更</span>
                </button>
              </div>
            </div>

            <div class="action-group">
              <h3 class="action-group-title">增量更新</h3>
              <div class="action-buttons">
                <button
                  @click="showGeneratePatchModal = true"
                  class="action-button"
                >
                  <ArrowPathIcon class="w-5 h-5 mb-2" />
                  <span class="action-label">生成增量包</span>
                  <span class="action-description">生成版本间的增量更新包</span>
                </button>
                <button
                  @click="showPatchHistoryModal = true"
                  class="action-button"
                >
                  <ClockIcon class="w-5 h-5 mb-2" />
                  <span class="action-label">更新历史</span>
                  <span class="action-description">查看热更新历史记录</span>
                </button>
              </div>
            </div>

            <div class="action-group">
              <h3 class="action-group-title">维护工具</h3>
              <div class="action-buttons">
                <button
                  @click="cleanCache"
                  :disabled="isCleaningCache"
                  class="action-button"
                >
                  <TrashIcon class="w-5 h-5 mb-2" />
                  <span class="action-label">{{ isCleaningCache ? '清理中...' : '清理缓存' }}</span>
                  <span class="action-description">清理热更新缓存文件</span>
                </button>
                <button
                  @click="showConfigModal = true"
                  class="action-button"
                >
                  <CogIcon class="w-5 h-5 mb-2" />
                  <span class="action-label">配置管理</span>
                  <span class="action-description">管理热更新配置</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">统计信息</h2>
        </div>
        <div class="card-content">
          <div v-if="isLoadingStats" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载统计信息...</p>
          </div>

          <div v-else class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <DocumentTextIcon class="w-6 h-6 text-blue-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.totalManifests || 0 }}</div>
                <div class="stat-label">资源清单</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <ArrowPathIcon class="w-6 h-6 text-green-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.totalPatches || 0 }}</div>
                <div class="stat-label">增量更新包</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <ServerIcon class="w-6 h-6 text-purple-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ formatSize(stats.totalSize || 0) }}</div>
                <div class="stat-label">总更新大小</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <DevicePhoneMobileIcon class="w-6 h-6 text-orange-500" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ Object.keys(stats.byPlatform || {}).length }}</div>
                <div class="stat-label">支持平台</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近更新 -->
    <div class="recent-updates-section">
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">最近更新</h2>
          <div class="card-actions">
            <VCSelect
              v-model="recentFilter"
              :options="recentFilterOptions"
              placeholder="筛选平台"
              class="filter-select"
            />
            <button
              @click="showPatchHistoryModal = true"
              class="btn btn-secondary btn-sm"
            >
              <EyeIcon class="w-4 h-4 mr-2" />
              查看全部
            </button>
          </div>
        </div>
        <div class="card-content">
          <div v-if="isLoadingHistory" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载更新历史...</p>
          </div>

          <div v-else-if="filteredRecentUpdates.length === 0" class="empty-state">
            <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">暂无更新记录</p>
          </div>

          <div v-else class="updates-timeline">
            <div
              v-for="(update, index) in filteredRecentUpdates.slice(0, 5)"
              :key="update.id || index"
              class="timeline-item"
            >
              <div class="timeline-marker" :class="getUpdateMarkerClass(update)">
                <component :is="getUpdateIcon(update)" class="w-4 h-4" />
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <div class="update-info">
                    <span class="update-type">{{ getUpdateTypeText(update.type) }}</span>
                    <span class="update-platform">{{ getPlatformName(update.platform) }}</span>
                  </div>
                  <div class="timeline-status">
                    <span :class="['status-badge', update.success ? 'status-success' : 'status-error']">
                      {{ update.success ? '成功' : '失败' }}
                    </span>
                  </div>
                </div>
                <div class="timeline-details">
                  <div class="update-meta">
                    <span class="timestamp">{{ formatTime(update.timestamp) }}</span>
                    <span v-if="update.size" class="update-size">
                      大小: {{ formatSize(update.size) }}
                    </span>
                    <span v-if="update.version" class="update-version">
                      版本: {{ update.version }}
                    </span>
                  </div>
                  <div v-if="update.error" class="error-message">
                    <ExclamationCircleIcon class="w-4 h-4 mr-1 text-red-500" />
                    {{ update.error }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框组件（暂时注释掉） -->
    <!--
    <GenerateManifestModal
      v-model="showGenerateManifestModal"
      @success="onManifestGenerated"
    />

    <GeneratePatchModal
      v-model="showGeneratePatchModal"
      @success="onPatchGenerated"
    />

    <CheckChangesModal
      v-model="showCheckChangesModal"
    />

    <PatchHistoryModal
      v-model="showPatchHistoryModal"
      :patch-history="patchHistory"
    />

    <HotUpdateConfigModal
      v-model="showConfigModal"
      @save="onConfigSaved"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  ArrowPathIcon,
  DocumentPlusIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  TrashIcon,
  CogIcon,
  ServerIcon,
  DevicePhoneMobileIcon,
  EyeIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../shared/services/ErrorHandler';
import { NotificationService } from '../../shared/services/NotificationService';

// 导入基础组件
import VCSelect from '../components/ui/VCSelect.vue';

// 导入模态框组件（暂时注释掉，稍后创建）
// import GenerateManifestModal from '../components/modals/GenerateManifestModal.vue';
// import GeneratePatchModal from '../components/modals/GeneratePatchModal.vue';
// import CheckChangesModal from '../components/modals/CheckChangesModal.vue';
// import PatchHistoryModal from '../components/modals/PatchHistoryModal.vue';
// import HotUpdateConfigModal from '../components/modals/HotUpdateConfigModal.vue';

// 响应式数据
const isLoading = ref(false);
const isLoadingStats = ref(false);
const isLoadingHistory = ref(false);
const isCleaningCache = ref(false);
const recentFilter = ref('all');

// 模态框状态
const showGenerateManifestModal = ref(false);
const showGeneratePatchModal = ref(false);
const showCheckChangesModal = ref(false);
const showPatchHistoryModal = ref(false);
const showConfigModal = ref(false);

// 数据
const stats = ref<any>({});
const recentUpdates = ref<any[]>([]);
const patchHistory = ref<any[]>([]);

// 计算属性
const recentFilterOptions = computed(() => [
  { label: '全部平台', value: 'all' },
  { label: 'Web 移动端', value: 'web-mobile' },
  { label: 'Android', value: 'android' },
  { label: 'iOS', value: 'ios' }
]);

const filteredRecentUpdates = computed(() => {
  if (recentFilter.value === 'all') {
    return recentUpdates.value;
  }

  return recentUpdates.value.filter(update => update.platform === recentFilter.value);
});

// 生命周期
onMounted(async () => {
  await loadData();
});

// 方法
const loadData = async () => {
  await Promise.all([
    loadStats(),
    loadRecentUpdates(),
    loadPatchHistory()
  ]);
};

const loadStats = async () => {
  try {
    isLoadingStats.value = true;
    const result = await apiClient.hotupdate.getConfig();

    if (result.success && result.data) {
      // 从配置信息中生成统计数据
      stats.value = {
        totalManifests: result.data.manifests?.length || 0,
        totalPatches: result.data.patches?.length || 0,
        totalSize: result.data.totalSize || 0,
        byPlatform: result.data.byPlatform || {}
      };
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Hot Update Stats');
  } finally {
    isLoadingStats.value = false;
  }
};

const loadRecentUpdates = async () => {
  try {
    isLoadingHistory.value = true;
    // 暂时使用模拟数据，因为 API 中没有 getRecentUpdates 方法
    recentUpdates.value = [];
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Recent Updates');
  } finally {
    isLoadingHistory.value = false;
  }
};

const loadPatchHistory = async () => {
  try {
    // 暂时使用模拟数据，因为 API 中没有 getHistory 方法
    patchHistory.value = [];
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Load Patch History');
  }
};

const refreshData = async () => {
  isLoading.value = true;
  await loadData();
  isLoading.value = false;
  NotificationService.success('数据已刷新');
};

const cleanCache = async () => {
  const confirmed = confirm('确定要清理热更新缓存吗？这将删除所有缓存文件。');
  if (!confirmed) return;

  try {
    isCleaningCache.value = true;
    const result = await apiClient.hotupdate.clean();

    if (result.success) {
      NotificationService.success('缓存清理完成');
      await loadStats();
    } else {
      throw new Error(result.error || '清理缓存失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Clean Cache');
  } finally {
    isCleaningCache.value = false;
  }
};

// 事件处理
const onManifestGenerated = () => {
  NotificationService.success('资源清单生成成功');
  loadData();
};

const onPatchGenerated = () => {
  NotificationService.success('增量更新包生成成功');
  loadData();
};

const onConfigSaved = () => {
  NotificationService.success('配置保存成功');
};

// 工具方法
const getUpdateMarkerClass = (update: any) => {
  return update.success ? 'timeline-marker-success' : 'timeline-marker-error';
};

const getUpdateIcon = (update: any) => {
  return update.success ? CheckCircleIcon : XCircleIcon;
};

const getUpdateTypeText = (type: string) => {
  const typeMap = {
    manifest: '资源清单',
    patch: '增量更新',
    full: '完整更新'
  };
  return typeMap[type as keyof typeof typeMap] || type;
};

const getPlatformName = (platform: string) => {
  const nameMap = {
    'web-mobile': 'Web 移动端',
    'android': 'Android',
    'ios': 'iOS'
  };
  return nameMap[platform as keyof typeof nameMap] || platform;
};

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();

  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;

  return time.toLocaleString('zh-CN');
};
</script>

<style scoped>
/* 热更新管理页面样式 */
.hot-update-management {
  @apply p-6 space-y-6 max-w-7xl mx-auto;
}

/* 页面头部 */
.page-header {
  @apply bg-white rounded-lg shadow-sm border p-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-info {
  @apply flex-1;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.page-subtitle {
  @apply text-gray-600;
}

.header-actions {
  @apply flex items-center space-x-3;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm border;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-actions {
  @apply flex items-center space-x-3;
}

.card-content {
  @apply p-6;
}

/* 快速操作 */
.actions-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.action-group {
  @apply space-y-4;
}

.action-group-title {
  @apply text-base font-semibold text-gray-900;
}

.action-buttons {
  @apply grid grid-cols-1 gap-3;
}

.action-button {
  @apply flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 cursor-pointer;
}

.action-button:hover {
  @apply shadow-sm;
}

.action-label {
  @apply text-sm font-medium text-gray-900 mb-1;
}

.action-description {
  @apply text-xs text-gray-500 text-center;
}

/* 统计信息 */
.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stat-card {
  @apply flex items-center space-x-4 p-4 bg-gray-50 rounded-lg;
}

.stat-icon {
  @apply flex-shrink-0;
}

.stat-content {
  @apply flex-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 最近更新时间线 */
.updates-timeline {
  @apply space-y-6;
}

.timeline-item {
  @apply relative flex items-start space-x-4;
}

.timeline-item:not(:last-child)::before {
  content: '';
  @apply absolute left-4 top-8 w-0.5 h-full bg-gray-200;
}

.timeline-marker {
  @apply flex items-center justify-center w-8 h-8 rounded-full border-2 bg-white;
}

.timeline-marker-success {
  @apply border-green-500 text-green-500;
}

.timeline-marker-error {
  @apply border-red-500 text-red-500;
}

.timeline-content {
  @apply flex-1 min-w-0;
}

.timeline-header {
  @apply flex items-center justify-between mb-2;
}

.update-info {
  @apply flex items-center space-x-3;
}

.update-type {
  @apply font-medium text-gray-900;
}

.update-platform {
  @apply text-sm text-gray-600;
}

.timeline-status {
  @apply flex items-center;
}

.status-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-success {
  @apply bg-green-100 text-green-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.timeline-details {
  @apply space-y-2;
}

.update-meta {
  @apply flex items-center space-x-4 text-xs text-gray-500;
}

.error-message {
  @apply flex items-center text-sm text-red-600 bg-red-50 p-2 rounded;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

/* 加载和空状态 */
.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.filter-select {
  @apply w-48;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hot-update-management {
    @apply p-4 space-y-4;
  }

  .header-content {
    @apply flex-col items-start space-y-4;
  }

  .actions-grid {
    @apply grid-cols-1;
  }

  .stats-grid {
    @apply grid-cols-1;
  }

  .update-meta {
    @apply flex-col items-start space-y-1 space-x-0;
  }
}
</style>
