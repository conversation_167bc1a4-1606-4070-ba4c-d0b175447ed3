<template>
  <div id="app" class="h-screen bg-gray-50 flex flex-col">
    <!-- 全局错误边界 -->
    <ErrorBoundary>
    <!-- 标题栏 -->
    <div class="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between drag-region">
      <div class="flex items-center space-x-3">
        <div class="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
          <span class="text-white text-xs font-bold">VC</span>
        </div>
        <h1 class="text-lg font-semibold text-gray-900">Version-Craft GUI</h1>
      </div>
      
      <div class="flex items-center space-x-2 text-sm text-gray-600">
        <span v-if="currentProject">{{ projectName }}</span>
        <span v-else class="text-gray-400">未选择项目</span>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 侧边栏 -->
      <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
        <!-- 项目选择 -->
        <div class="p-4 border-b border-gray-200">
          <button
            @click="selectProject"
            class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FolderOpenIcon class="w-4 h-4 mr-2" />
            {{ currentProject ? '切换项目' : '选择项目' }}
          </button>
        </div>

        <!-- 导航菜单 -->
        <nav class="flex-1 p-4">
          <div class="space-y-2">
            <router-link
              to="/"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/' }"
            >
              <HomeIcon class="w-4 h-4 mr-3" />
              项目概览
            </router-link>
            
            <router-link
              to="/version"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/version' }"
            >
              <TagIcon class="w-4 h-4 mr-3" />
              版本管理
            </router-link>
            
            <router-link
              to="/build"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/build' }"
            >
              <CogIcon class="w-4 h-4 mr-3" />
              构建管理
            </router-link>

            <router-link
              to="/deploy"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/deploy' }"
            >
              <RocketLaunchIcon class="w-4 h-4 mr-3" />
              部署管理
            </router-link>

            <router-link
              to="/rollback"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/rollback' }"
            >
              <ArrowUturnLeftIcon class="w-4 h-4 mr-3" />
              回滚管理
            </router-link>

            <router-link
              to="/config"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/config' }"
            >
              <AdjustmentsHorizontalIcon class="w-4 h-4 mr-3" />
              项目配置
            </router-link>

            <router-link
              to="/hotupdate"
              class="nav-item"
              :class="{ 'nav-item-active': $route.path === '/hotupdate' }"
            >
              <FireIcon class="w-4 h-4 mr-3" />
              热更新
            </router-link>
          </div>
        </nav>

        <!-- 底部信息 -->
        <div class="p-4 border-t border-gray-200 text-xs text-gray-500">
          <div class="flex items-center justify-between">
            <span>Version-Craft GUI</span>
            <span>v1.0.0</span>
          </div>
        </div>
      </div>

      <!-- 主内容 -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 内容区域 -->
        <main class="flex-1 overflow-auto">
          <div v-if="!currentProject" class="h-full flex items-center justify-center">
            <div class="text-center">
              <FolderIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h2 class="text-xl font-semibold text-gray-900 mb-2">欢迎使用 Version-Craft GUI</h2>
              <p class="text-gray-600 mb-6">请选择一个 Version-Craft 项目开始使用</p>
              <button
                @click="selectProject"
                class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <FolderOpenIcon class="w-5 h-5 mr-2" />
                选择项目目录
              </button>
            </div>
          </div>
          
          <router-view v-else />
        </main>
      </div>
    </div>

    <!-- 全局通知 -->
    <Notification
      v-if="notification.show"
      :type="notification.type"
      :message="notification.message"
      @close="hideNotification"
    />

    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-900">{{ loadingMessage }}</span>
      </div>
    </div>

    <!-- 全局通知容器 -->
    <NotificationContainer />
    </ErrorBoundary>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  HomeIcon,
  TagIcon,
  CogIcon,
  RocketLaunchIcon,
  ArrowUturnLeftIcon,
  AdjustmentsHorizontalIcon,
  FolderIcon,
  FolderOpenIcon,
  FireIcon
} from '@heroicons/vue/24/outline';

import Notification from './components/Notification.vue';
import { useAppStore } from './stores/app';

// 组合式 API
const router = useRouter();
const appStore = useAppStore();

// 响应式数据
const isLoading = ref(false);
const loadingMessage = ref('');
const notification = ref({
  show: false,
  type: 'info' as 'success' | 'error' | 'warning' | 'info',
  message: ''
});

// 计算属性
const currentProject = computed(() => appStore.currentProject);
const projectName = computed(() => {
  if (!currentProject.value) return '';
  return currentProject.value.split(/[/\\]/).pop() || '';
});

// 方法
const selectProject = async () => {
  try {
    isLoading.value = true;
    loadingMessage.value = '选择项目目录...';

    if (!window.electronAPI) {
      showNotification('error', 'Electron API 不可用');
      return;
    }

    const result = await window.electronAPI.selectProjectDirectory();

    if (result.success && result.path) {
      await appStore.setCurrentProject(result.path);
      showNotification('success', '项目加载成功');

      // 导航到项目概览页面
      router.push('/');
    } else if (result.error) {
      showNotification('error', result.error);
    }
  } catch (error) {
    showNotification('error', '选择项目失败');
    console.error('Select project error:', error);
  } finally {
    isLoading.value = false;
  }
};

const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
  notification.value = {
    show: true,
    type,
    message
  };
  
  // 3秒后自动隐藏
  setTimeout(() => {
    hideNotification();
  }, 3000);
};

const hideNotification = () => {
  notification.value.show = false;
};

// 事件监听
const setupEventListeners = () => {
  // 检查 electronAPI 是否可用
  if (!window.electronAPI) {
    console.warn('electronAPI 不可用，跳过事件监听设置');
    return;
  }

  // 监听版本变更
  window.electronAPI.onVersionChanged((_data) => {
    showNotification('info', '版本已更新');
    appStore.refreshCurrentVersion();
  });

  // 监听构建进度
  window.electronAPI.onBuildProgress((data) => {
    if (data.completed) {
      if (data.success) {
        showNotification('success', '构建完成');
      } else {
        showNotification('error', `构建失败: ${data.error}`);
      }
    }
  });
};

const cleanupEventListeners = () => {
  if (window.electronAPI) {
    window.electronAPI.offVersionChanged();
    window.electronAPI.offBuildProgress();
  }
};

// 生命周期
onMounted(async () => {
  // 设置事件监听
  setupEventListeners();

  // 检查是否有保存的项目路径
  try {
    if (window.electronAPI) {
      const savedProject = await window.electronAPI.getCurrentProject();
      if (savedProject) {
        await appStore.setCurrentProject(savedProject);
      }
    }
  } catch (error) {
    console.error('Load saved project error:', error);
  }
});

onUnmounted(() => {
  cleanupEventListeners();
});
</script>

<style scoped>
/* 拖拽区域 */
.drag-region {
  -webkit-app-region: drag;
}

.drag-region button {
  -webkit-app-region: no-drag;
}

/* 导航项样式 */
.nav-item {
  @apply flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors;
}

.nav-item-active {
  @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
}

/* 自定义滚动条 */
:deep(.overflow-auto) {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

:deep(.overflow-auto::-webkit-scrollbar) {
  width: 6px;
}

:deep(.overflow-auto::-webkit-scrollbar-track) {
  background: #f7fafc;
}

:deep(.overflow-auto::-webkit-scrollbar-thumb) {
  background: #cbd5e0;
  border-radius: 3px;
}

:deep(.overflow-auto::-webkit-scrollbar-thumb:hover) {
  background: #a0aec0;
}
</style>
