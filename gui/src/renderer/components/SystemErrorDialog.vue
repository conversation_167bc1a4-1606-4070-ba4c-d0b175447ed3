<!--
  系统错误对话框组件
  专门用于显示系统级错误，如 Electron API 不可用等
-->

<template>
  <Teleport to="body">
    <Transition name="dialog" appear>
      <div v-if="visible" class="system-error-overlay" @click="handleOverlayClick">
        <div class="system-error-dialog" @click.stop>
          <!-- 错误图标 -->
          <div class="error-icon-container">
            <ExclamationTriangleIcon class="error-icon" />
          </div>

          <!-- 错误标题 -->
          <div class="error-header">
            <h3 class="error-title">{{ title || '系统错误' }}</h3>
            <button 
              @click="handleClose"
              class="close-button"
              aria-label="关闭"
            >
              <XMarkIcon class="w-5 h-5" />
            </button>
          </div>

          <!-- 错误消息 -->
          <div class="error-content">
            <p class="error-message">{{ message }}</p>
            
            <!-- 详细信息（可选） -->
            <div v-if="details" class="error-details">
              <button 
                @click="showDetails = !showDetails"
                class="details-toggle"
              >
                {{ showDetails ? '隐藏详情' : '显示详情' }}
                <ChevronDownIcon 
                  :class="{ 'rotate-180': showDetails }"
                  class="w-4 h-4 ml-1 transition-transform"
                />
              </button>
              
              <div v-if="showDetails" class="details-content">
                <pre class="details-text">{{ details }}</pre>
              </div>
            </div>

            <!-- 建议操作 -->
            <div v-if="suggestions && suggestions.length > 0" class="suggestions">
              <h4 class="suggestions-title">建议解决方案：</h4>
              <ul class="suggestions-list">
                <li v-for="(suggestion, index) in suggestions" :key="index">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="error-actions">
            <button 
              v-if="showRetry"
              @click="handleRetry"
              class="action-button retry-button"
            >
              <ArrowPathIcon class="w-4 h-4 mr-2" />
              重试
            </button>
            
            <button 
              v-if="showReload"
              @click="handleReload"
              class="action-button reload-button"
            >
              <ArrowPathIcon class="w-4 h-4 mr-2" />
              重新加载
            </button>
            
            <button 
              @click="handleClose"
              class="action-button close-button-action"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  ExclamationTriangleIcon,
  XMarkIcon,
  ChevronDownIcon,
  ArrowPathIcon
} from '@heroicons/vue/24/outline';

interface Props {
  visible: boolean;
  title?: string;
  message: string;
  details?: string;
  suggestions?: string[];
  showRetry?: boolean;
  showReload?: boolean;
  closeOnOverlay?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '系统错误',
  suggestions: () => [
    '检查网络连接是否正常',
    '重新启动应用程序',
    '清除应用缓存后重试',
    '如果问题持续存在，请联系技术支持'
  ],
  showRetry: true,
  showReload: true,
  closeOnOverlay: true
});

const emit = defineEmits<{
  close: [];
  retry: [];
  reload: [];
}>();

// 响应式数据
const showDetails = ref(false);

// 方法
const handleClose = () => {
  emit('close');
};

const handleRetry = () => {
  emit('retry');
};

const handleReload = () => {
  emit('reload');
  // 重新加载页面
  window.location.reload();
};

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose();
  }
};
</script>

<style scoped>
/* 遮罩层 */
.system-error-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
}

/* 对话框容器 */
.system-error-dialog {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full max-h-screen overflow-y-auto;
  min-width: 400px;
}

/* 错误图标 */
.error-icon-container {
  @apply flex justify-center pt-6 pb-4;
}

.error-icon {
  @apply w-12 h-12 text-red-500;
}

/* 错误头部 */
.error-header {
  @apply flex items-center justify-between px-6 pb-4;
}

.error-title {
  @apply text-lg font-semibold text-gray-900;
}

.close-button {
  @apply text-gray-400 hover:text-gray-600 transition-colors p-1;
}

/* 错误内容 */
.error-content {
  @apply px-6 pb-6;
}

.error-message {
  @apply text-gray-700 mb-4 leading-relaxed;
  word-wrap: break-word;
}

/* 详细信息 */
.error-details {
  @apply mb-4;
}

.details-toggle {
  @apply flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors;
}

.details-content {
  @apply mt-3 p-3 bg-gray-50 rounded border;
}

.details-text {
  @apply text-xs text-gray-600 whitespace-pre-wrap font-mono;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

/* 建议 */
.suggestions {
  @apply mt-4;
}

.suggestions-title {
  @apply text-sm font-medium text-gray-900 mb-2;
}

.suggestions-list {
  @apply text-sm text-gray-600 space-y-1;
}

.suggestions-list li {
  @apply flex items-start;
}

.suggestions-list li::before {
  content: "•";
  @apply text-blue-500 font-bold mr-2 mt-0.5;
}

/* 操作按钮 */
.error-actions {
  @apply flex justify-end space-x-3 px-6 py-4 bg-gray-50 border-t;
}

.action-button {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.retry-button {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.reload-button {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.close-button-action {
  @apply bg-gray-300 text-gray-700 hover:bg-gray-400 focus:ring-gray-500;
}

/* 动画 */
.dialog-enter-active,
.dialog-leave-active {
  @apply transition-all duration-300 ease-out;
}

.dialog-enter-from,
.dialog-leave-to {
  @apply opacity-0;
}

.dialog-enter-from .system-error-dialog,
.dialog-leave-to .system-error-dialog {
  @apply transform scale-95;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .system-error-dialog {
    min-width: auto;
    @apply mx-2;
  }
  
  .error-actions {
    @apply flex-col space-x-0 space-y-2;
  }
  
  .action-button {
    @apply w-full justify-center;
  }
}
</style>
