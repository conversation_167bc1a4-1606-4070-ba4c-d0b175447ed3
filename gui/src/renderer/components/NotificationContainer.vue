<!--
  全局通知容器组件
  显示所有系统通知
-->

<template>
  <Teleport to="body">
    <!-- 通知容器 -->
    <div class="notification-container">
      <TransitionGroup name="notification" tag="div" class="notification-list">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="getNotificationClasses(notification)"
          class="notification-item"
        >
          <!-- 通知图标 -->
          <div class="notification-icon">
            <component :is="getNotificationIcon(notification.type)" class="w-5 h-5" />
          </div>

          <!-- 通知内容 -->
          <div class="notification-content">
            <div class="notification-header">
              <h4 class="notification-title">{{ notification.title }}</h4>
              <button
                @click="removeNotification(notification.id)"
                class="notification-close"
                aria-label="关闭通知"
              >
                <XMarkIcon class="w-4 h-4" />
              </button>
            </div>
            
            <p class="notification-message">{{ notification.message }}</p>
            
            <!-- 进度条 -->
            <div v-if="notification.progress !== undefined" class="notification-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ width: `${notification.progress}%` }"
                ></div>
              </div>
              <span class="progress-text">{{ Math.round(notification.progress) }}%</span>
            </div>

            <!-- 操作按钮 -->
            <div v-if="notification.actions && notification.actions.length > 0" class="notification-actions">
              <button
                v-for="action in notification.actions"
                :key="action.label"
                @click="handleAction(action, notification.id)"
                :class="getActionClasses(action.variant)"
                class="action-button"
              >
                {{ action.label }}
              </button>
            </div>
          </div>

          <!-- 自动消失倒计时 -->
          <div 
            v-if="!notification.persistent && notification.duration > 0"
            class="notification-timer"
            :style="{ animationDuration: `${notification.duration}ms` }"
          ></div>
        </div>
      </TransitionGroup>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container">
      <TransitionGroup name="toast" tag="div" class="toast-list">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="getToastClasses(toast)"
          class="toast-item"
        >
          <div class="toast-content">
            <component :is="getNotificationIcon(toast.type)" class="w-4 h-4 toast-icon" />
            <span class="toast-message">{{ toast.message }}</span>
            <button
              @click="removeToast(toast.id)"
              class="toast-close"
              aria-label="关闭提示"
            >
              <XMarkIcon class="w-3 h-3" />
            </button>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  CheckCircleIcon, 
  ExclamationCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon,
  XMarkIcon 
} from '@heroicons/vue/24/outline';
import { notifications, toasts } from '@shared/services/NotificationService.ts';
import { NotificationService, ToastService } from '@shared/services/NotificationService.ts';
import type { NotificationAction } from '@shared/services/ErrorHandler.ts';

// 计算属性
const notificationList = computed(() => notifications.value);
const toastList = computed(() => toasts.value);

// 方法
const getNotificationClasses = (notification: any) => {
  const baseClasses = 'notification';
  const typeClasses = {
    success: 'notification-success',
    error: 'notification-error',
    warning: 'notification-warning',
    info: 'notification-info'
  };
  
  return [baseClasses, typeClasses[notification.type as keyof typeof typeClasses]];
};

const getToastClasses = (toast: any) => {
  const baseClasses = 'toast';
  const typeClasses = {
    success: 'toast-success',
    error: 'toast-error',
    warning: 'toast-warning',
    info: 'toast-info'
  };
  
  return [baseClasses, typeClasses[toast.type as keyof typeof typeClasses]];
};

const getNotificationIcon = (type: string) => {
  const icons = {
    success: CheckCircleIcon,
    error: ExclamationCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon
  };
  
  return icons[type as keyof typeof icons] || InformationCircleIcon;
};

const getActionClasses = (variant?: string) => {
  const baseClasses = 'btn btn-sm';
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary'
  };
  
  return [baseClasses, variantClasses[variant as keyof typeof variantClasses] || 'btn-secondary'];
};

const removeNotification = (id: string) => {
  NotificationService.remove(id);
};

const removeToast = (id: string) => {
  ToastService.remove(id);
};

const handleAction = (action: NotificationAction, notificationId: string) => {
  try {
    action.action();
    // 执行操作后移除通知
    removeNotification(notificationId);
  } catch (error) {
    console.error('Error executing notification action:', error);
  }
};
</script>

<style scoped>
/* {{ AURA: Modify - 优化通知容器尺寸，确保内容完整显示 }} */
.notification-container {
  @apply fixed top-4 right-4 z-50 space-y-2;
  max-width: 450px; /* 增加最大宽度 */
  min-width: 350px; /* 设置最小宽度 */
}

.notification-list {
  @apply space-y-2;
}

.notification-item {
  @apply bg-white rounded-lg shadow-lg border p-4 relative;
  min-width: 350px; /* 增加最小宽度 */
  max-width: 450px; /* 设置最大宽度 */
  word-wrap: break-word; /* 长文本自动换行 */
  overflow-wrap: break-word; /* 兼容性 */
  box-sizing: border-box; /* 确保padding计算正确 */
}

.notification {
  @apply border-l-4;
}

.notification-success {
  @apply border-l-green-500;
}

.notification-error {
  @apply border-l-red-500;
}

.notification-warning {
  @apply border-l-yellow-500;
}

.notification-info {
  @apply border-l-blue-500;
}

.notification-icon {
  @apply absolute top-4 left-4;
}

.notification-success .notification-icon {
  @apply text-green-500;
}

.notification-error .notification-icon {
  @apply text-red-500;
}

.notification-warning .notification-icon {
  @apply text-yellow-500;
}

.notification-info .notification-icon {
  @apply text-blue-500;
}

/* {{ AURA: Modify - 优化通知内容布局，确保文本完整显示 }} */
.notification-content {
  @apply ml-8;
  flex: 1; /* 确保内容区域占满剩余空间 */
  min-width: 0; /* 允许内容收缩 */
}

.notification-header {
  @apply flex items-start justify-between mb-1;
  gap: 8px; /* 标题和关闭按钮之间的间距 */
}

.notification-title {
  @apply font-medium text-gray-900 text-sm;
  flex: 1; /* 标题占据剩余空间 */
  word-wrap: break-word; /* 长标题自动换行 */
  line-height: 1.4; /* 增加行高提高可读性 */
}

.notification-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
  flex-shrink: 0; /* 关闭按钮不收缩 */
  padding: 2px; /* 增加点击区域 */
}

.notification-message {
  @apply text-gray-700 text-sm mb-3;
  word-wrap: break-word; /* 长消息自动换行 */
  line-height: 1.5; /* 增加行高提高可读性 */
  white-space: pre-wrap; /* 保留换行符 */
}

.notification-progress {
  @apply flex items-center space-x-2 mb-3;
}

.progress-bar {
  @apply flex-1 bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply bg-blue-500 h-full transition-all duration-300 ease-out;
}

.progress-text {
  @apply text-xs text-gray-600 font-medium;
}

.notification-actions {
  @apply flex space-x-2;
}

.action-button {
  @apply px-3 py-1 text-xs font-medium rounded transition-colors;
}

.notification-timer {
  @apply absolute bottom-0 left-0 h-1 bg-gray-300;
  animation: timer-countdown linear;
}

@keyframes timer-countdown {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* {{ AURA: Modify - 优化Toast容器尺寸 }} */
.toast-container {
  @apply fixed bottom-4 right-4 z-50 space-y-2;
  max-width: 400px; /* 设置最大宽度 */
}

.toast-list {
  @apply space-y-2;
}

.toast-item {
  @apply rounded-md shadow-md px-4 py-3;
  min-width: 300px; /* 增加最小宽度 */
  max-width: 400px; /* 设置最大宽度 */
  word-wrap: break-word; /* 长文本自动换行 */
  box-sizing: border-box; /* 确保padding计算正确 */
}

.toast-success {
  @apply bg-green-500 text-white;
}

.toast-error {
  @apply bg-red-500 text-white;
}

.toast-warning {
  @apply bg-yellow-500 text-white;
}

.toast-info {
  @apply bg-blue-500 text-white;
}

/* {{ AURA: Modify - 优化Toast内容布局 }} */
.toast-content {
  @apply flex items-start space-x-2; /* 改为items-start确保多行文本对齐 */
  gap: 8px; /* 使用gap替代space-x以获得更好的控制 */
}

.toast-icon {
  @apply flex-shrink-0;
  margin-top: 2px; /* 图标与文本顶部对齐 */
}

.toast-message {
  @apply flex-1 text-sm font-medium;
  word-wrap: break-word; /* 长消息自动换行 */
  line-height: 1.4; /* 增加行高 */
  min-width: 0; /* 允许收缩 */
}

.toast-close {
  @apply text-white/80 hover:text-white transition-colors;
  flex-shrink: 0; /* 关闭按钮不收缩 */
  padding: 2px; /* 增加点击区域 */
  margin-top: 1px; /* 与文本对齐 */
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-sm {
  @apply px-2 py-1 text-xs;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

/* 动画 */
.notification-enter-active,
.notification-leave-active {
  @apply transition-all duration-300 ease-out;
}

.notification-enter-from {
  @apply opacity-0 transform translate-x-full;
}

.notification-leave-to {
  @apply opacity-0 transform translate-x-full;
}

.toast-enter-active,
.toast-leave-active {
  @apply transition-all duration-200 ease-out;
}

.toast-enter-from {
  @apply opacity-0 transform translate-y-2;
}

.toast-leave-to {
  @apply opacity-0 transform translate-y-2;
}

/* {{ AURA: Add - 响应式设计，适配不同屏幕尺寸 }} */
@media (max-width: 640px) {
  .notification-container {
    @apply left-4 right-4 top-4;
    max-width: none;
    min-width: auto;
  }

  .notification-item {
    min-width: auto;
    max-width: none;
  }

  .toast-container {
    @apply left-4 right-4 bottom-4;
    max-width: none;
  }

  .toast-item {
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .notification-container {
    @apply left-2 right-2 top-2;
  }

  .notification-item {
    @apply p-3; /* 减少内边距 */
  }

  .notification-content {
    @apply ml-6; /* 减少左边距 */
  }

  .toast-container {
    @apply left-2 right-2 bottom-2;
  }

  .toast-item {
    @apply px-3 py-2; /* 减少内边距 */
  }
}
</style>
