<!--
  部署历史模态框
  用于查看完整的部署历史记录
-->

<template>
  <VCModal 
    v-model="visible" 
    title="部署历史记录" 
    :loading="isLoading"
    size="extra-large"
    @cancel="handleCancel"
  >
    <div class="deploy-history">
      <!-- 筛选和搜索 -->
      <div class="history-filters">
        <div class="filters-left">
          <VCSelect 
            v-model="filters.platform"
            :options="platformOptions"
            placeholder="筛选平台"
            class="filter-select"
          />
          <VCSelect 
            v-model="filters.environment"
            :options="environmentOptions"
            placeholder="筛选环境"
            class="filter-select"
          />
          <VCSelect 
            v-model="filters.status"
            :options="statusOptions"
            placeholder="筛选状态"
            class="filter-select"
          />
        </div>
        <div class="filters-right">
          <VCInput 
            v-model="searchQuery"
            placeholder="搜索部署记录..."
            :prefix-icon="MagnifyingGlassIcon"
            class="search-input"
          />
          <button 
            @click="exportHistory" 
            class="btn btn-secondary btn-sm"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            导出
          </button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="history-stats">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ totalDeployments }}</div>
            <div class="stat-label">总部署次数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ successfulDeployments }}</div>
            <div class="stat-label">成功部署</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ failedDeployments }}</div>
            <div class="stat-label">失败部署</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </div>

      <!-- 部署记录列表 -->
      <div class="history-list">
        <div v-if="isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载部署历史...</p>
        </div>
        
        <div v-else-if="filteredHistory.length === 0" class="empty-state">
          <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">没有找到匹配的部署记录</p>
        </div>
        
        <div v-else class="history-table">
          <table class="table">
            <thead>
              <tr>
                <th>时间</th>
                <th>平台</th>
                <th>环境</th>
                <th>状态</th>
                <th>耗时</th>
                <th>操作者</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="record in paginatedHistory" 
                :key="record.id"
                class="table-row"
                :class="{ 'row-failed': !record.success }"
              >
                <td>
                  <div class="timestamp-cell">
                    <span class="timestamp">{{ formatDateTime(record.timestamp) }}</span>
                    <span class="relative-time">{{ formatRelativeTime(record.timestamp) }}</span>
                  </div>
                </td>
                <td>
                  <div class="platform-cell">
                    <component :is="getPlatformIcon(record.platform)" class="w-4 h-4 mr-2" />
                    <span>{{ getPlatformName(record.platform) }}</span>
                  </div>
                </td>
                <td>
                  <span 
                    :class="['environment-badge', getEnvironmentClass(record.environment)]"
                  >
                    {{ record.environment === 'staging' ? '测试' : '生产' }}
                  </span>
                </td>
                <td>
                  <div class="status-cell">
                    <component 
                      :is="record.success ? CheckCircleIcon : XCircleIcon" 
                      :class="['w-4 h-4 mr-2', record.success ? 'text-green-500' : 'text-red-500']"
                    />
                    <span :class="record.success ? 'text-green-700' : 'text-red-700'">
                      {{ record.success ? '成功' : '失败' }}
                    </span>
                  </div>
                </td>
                <td>
                  <span class="duration">{{ formatDuration(record.duration) }}</span>
                </td>
                <td>
                  <span class="operator">{{ record.operator || '-' }}</span>
                </td>
                <td>
                  <div class="action-buttons">
                    <button 
                      @click="showDeployDetails(record)"
                      class="btn btn-sm btn-ghost"
                    >
                      <EyeIcon class="w-3 h-3 mr-1" />
                      详情
                    </button>
                    <button 
                      v-if="record.url"
                      @click="openDeployUrl(record.url)"
                      class="btn btn-sm btn-ghost"
                    >
                      <LinkIcon class="w-3 h-3 mr-1" />
                      访问
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button 
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="btn btn-sm btn-secondary"
          >
            上一页
          </button>
          <span class="page-info">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          <button 
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="btn btn-sm btn-secondary"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 部署详情模态框 -->
    <DeployDetailsModal 
      v-model="showDetailsModal"
      :deploy-record="selectedRecord"
    />

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          关闭
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  LinkIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon
} from '@heroicons/vue/24/outline';

import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCSelect from '../ui/VCSelect.vue';
import VCInput from '../ui/VCInput.vue';
import DeployDetailsModal from './DeployDetailsModal.vue';

// Props
interface Props {
  modelValue: boolean;
  deployHistory: any[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isLoading = ref(false);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const showDetailsModal = ref(false);
const selectedRecord = ref<any>(null);

const filters = ref({
  platform: '',
  environment: '',
  status: ''
});

// 计算属性
const platformOptions = computed(() => [
  { label: '全部平台', value: '' },
  { label: 'Web 移动端', value: 'web-mobile' },
  { label: 'Android', value: 'android' },
  { label: 'iOS', value: 'ios' }
]);

const environmentOptions = computed(() => [
  { label: '全部环境', value: '' },
  { label: '测试环境', value: 'staging' },
  { label: '生产环境', value: 'production' }
]);

const statusOptions = computed(() => [
  { label: '全部状态', value: '' },
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' }
]);

const filteredHistory = computed(() => {
  let result = props.deployHistory;
  
  // 平台筛选
  if (filters.value.platform) {
    result = result.filter(record => record.platform === filters.value.platform);
  }
  
  // 环境筛选
  if (filters.value.environment) {
    result = result.filter(record => record.environment === filters.value.environment);
  }
  
  // 状态筛选
  if (filters.value.status) {
    const isSuccess = filters.value.status === 'success';
    result = result.filter(record => record.success === isSuccess);
  }
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(record => 
      record.platform.toLowerCase().includes(query) ||
      record.operator?.toLowerCase().includes(query) ||
      record.notes?.toLowerCase().includes(query)
    );
  }
  
  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredHistory.value.length / pageSize.value);
});

const paginatedHistory = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredHistory.value.slice(start, end);
});

const totalDeployments = computed(() => props.deployHistory.length);

const successfulDeployments = computed(() => {
  return props.deployHistory.filter(record => record.success).length;
});

const failedDeployments = computed(() => {
  return props.deployHistory.filter(record => !record.success).length;
});

const successRate = computed(() => {
  if (totalDeployments.value === 0) return 0;
  return Math.round((successfulDeployments.value / totalDeployments.value) * 100);
});

// 方法
const getPlatformIcon = (platform: string) => {
  const iconMap = {
    'web-mobile': ComputerDesktopIcon,
    'android': DevicePhoneMobileIcon,
    'ios': DeviceTabletIcon
  };
  return iconMap[platform as keyof typeof iconMap] || ComputerDesktopIcon;
};

const getPlatformName = (platform: string) => {
  const nameMap = {
    'web-mobile': 'Web 移动端',
    'android': 'Android',
    'ios': 'iOS'
  };
  return nameMap[platform as keyof typeof nameMap] || platform;
};

const getEnvironmentClass = (environment: string) => {
  return environment === 'production' ? 'env-production' : 'env-staging';
};

const showDeployDetails = (record: any) => {
  selectedRecord.value = record;
  showDetailsModal.value = true;
};

const openDeployUrl = (url: string) => {
  window.open(url, '_blank');
};

const exportHistory = () => {
  const data = JSON.stringify(filteredHistory.value, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `deploy-history-${new Date().toISOString().slice(0, 10)}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  NotificationService.success('部署历史已导出');
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const formatRelativeTime = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now.getTime() - time.getTime();
  
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return time.toLocaleDateString('zh-CN');
};

const formatDuration = (ms: number) => {
  if (!ms) return '-';
  
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const handleCancel = () => {
  visible.value = false;
  // 重置筛选
  filters.value = {
    platform: '',
    environment: '',
    status: ''
  };
  searchQuery.value = '';
  currentPage.value = 1;
};
</script>

<style scoped>
/* 部署历史样式 */
.deploy-history {
  @apply space-y-6;
}

/* 筛选器 */
.history-filters {
  @apply flex items-center justify-between p-4 bg-gray-50 rounded-lg;
}

.filters-left {
  @apply flex items-center space-x-3;
}

.filters-right {
  @apply flex items-center space-x-3;
}

.filter-select {
  @apply w-40;
}

.search-input {
  @apply w-64;
}

/* 统计信息 */
.history-stats {
  @apply bg-white border rounded-lg p-4;
}

.stats-grid {
  @apply grid grid-cols-4 gap-4;
}

.stat-item {
  @apply text-center;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 历史列表 */
.history-list {
  @apply space-y-4;
}

.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

/* 表格样式 */
.history-table {
  @apply overflow-hidden rounded-lg border border-gray-200;
}

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table thead {
  @apply bg-gray-50;
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm;
}

.table-row {
  @apply bg-white hover:bg-gray-50 transition-colors;
}

.row-failed {
  @apply bg-red-50 hover:bg-red-100;
}

/* 表格单元格 */
.timestamp-cell {
  @apply space-y-1;
}

.timestamp {
  @apply block text-gray-900 font-medium;
}

.relative-time {
  @apply block text-xs text-gray-500;
}

.platform-cell {
  @apply flex items-center;
}

.environment-badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.env-staging {
  @apply bg-yellow-100 text-yellow-800;
}

.env-production {
  @apply bg-green-100 text-green-800;
}

.status-cell {
  @apply flex items-center;
}

.duration {
  @apply text-gray-600;
}

.operator {
  @apply text-gray-600;
}

.action-buttons {
  @apply flex items-center space-x-2;
}

/* 分页 */
.pagination {
  @apply flex items-center justify-between p-4 bg-gray-50 rounded-lg;
}

.page-info {
  @apply text-sm text-gray-600;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-ghost {
  @apply text-gray-600 hover:text-gray-800 hover:bg-gray-100;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-filters {
    @apply flex-col items-start space-y-3;
  }

  .filters-left,
  .filters-right {
    @apply w-full justify-between;
  }

  .filter-select,
  .search-input {
    @apply w-full;
  }

  .stats-grid {
    @apply grid-cols-2;
  }

  .history-table {
    @apply overflow-x-auto;
  }

  .table {
    @apply min-w-max;
  }

  .pagination {
    @apply flex-col space-y-2;
  }
}
</style>
