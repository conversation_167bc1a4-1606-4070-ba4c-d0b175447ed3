<!--
  部署模态框
  用于配置和启动部署任务
-->

<template>
  <VCModal 
    v-model="visible" 
    title="开始部署" 
    :loading="isDeploying"
    size="large"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="deploy-config">
      <!-- 部署目标选择 -->
      <div class="deploy-target">
        <h4 class="section-title">部署目标</h4>
        <div class="target-grid">
          <div class="target-group">
            <label class="target-label">目标环境 *</label>
            <div class="radio-group">
              <label class="radio-item">
                <input 
                  type="radio" 
                  v-model="deployConfig.environment" 
                  value="staging"
                  class="radio-input"
                />
                <div class="radio-content">
                  <span class="radio-label">测试环境</span>
                  <span class="radio-description">用于测试和验证的环境</span>
                </div>
              </label>
              <label class="radio-item">
                <input 
                  type="radio" 
                  v-model="deployConfig.environment" 
                  value="production"
                  class="radio-input"
                />
                <div class="radio-content">
                  <span class="radio-label">生产环境</span>
                  <span class="radio-description">面向用户的正式环境</span>
                </div>
              </label>
            </div>
          </div>
          
          <div class="target-group">
            <label class="target-label">目标平台</label>
            <div class="platform-checkboxes">
              <label 
                v-for="platform in supportedPlatforms" 
                :key="platform.key"
                class="platform-checkbox"
              >
                <input 
                  type="checkbox" 
                  v-model="deployConfig.platforms" 
                  :value="platform.key"
                  class="checkbox-input"
                />
                <div class="checkbox-content">
                  <component :is="platform.icon" class="w-5 h-5 platform-icon" />
                  <span class="checkbox-label">{{ platform.name }}</span>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 部署选项 -->
      <div class="deploy-options">
        <h4 class="section-title">部署选项</h4>
        <div class="options-grid">
          <div class="option-group">
            <h5 class="option-group-title">构建选项</h5>
            <div class="option-items">
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="deployConfig.options.skipBuild"
                  class="option-checkbox"
                />
                <div class="option-content">
                  <span class="option-label">跳过构建</span>
                  <span class="option-description">使用现有构建产物</span>
                </div>
              </label>
              
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="deployConfig.options.cleanBuild"
                  class="option-checkbox"
                />
                <div class="option-content">
                  <span class="option-label">清理构建</span>
                  <span class="option-description">删除旧构建文件后重新构建</span>
                </div>
              </label>
            </div>
          </div>
          
          <div class="option-group">
            <h5 class="option-group-title">部署选项</h5>
            <div class="option-items">
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="deployConfig.options.backup"
                  class="option-checkbox"
                />
                <div class="option-content">
                  <span class="option-label">创建备份</span>
                  <span class="option-description">部署前备份当前版本</span>
                </div>
              </label>
              
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="deployConfig.options.verify"
                  class="option-checkbox"
                />
                <div class="option-content">
                  <span class="option-label">部署验证</span>
                  <span class="option-description">部署后验证应用可用性</span>
                </div>
              </label>
            </div>
          </div>
          
          <div class="option-group">
            <h5 class="option-group-title">通知选项</h5>
            <div class="option-items">
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="deployConfig.options.notify"
                  class="option-checkbox"
                />
                <div class="option-content">
                  <span class="option-label">发送通知</span>
                  <span class="option-description">部署完成后发送通知</span>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 部署说明 -->
      <div class="deploy-notes">
        <h4 class="section-title">部署说明</h4>
        <VCTextarea 
          v-model="deployConfig.notes"
          placeholder="请输入本次部署的说明（可选）"
          :rows="3"
        />
      </div>

      <!-- 部署预览 -->
      <div class="deploy-preview">
        <h4 class="section-title">部署预览</h4>
        <div class="preview-card">
          <div class="preview-header">
            <RocketLaunchIcon class="w-5 h-5 text-blue-500" />
            <span class="preview-title">部署配置预览</span>
          </div>
          <div class="preview-content">
            <div class="preview-item">
              <span class="preview-label">目标环境:</span>
              <span class="preview-value" :class="getEnvClass(deployConfig.environment)">
                {{ deployConfig.environment === 'staging' ? '测试环境' : '生产环境' }}
              </span>
            </div>
            <div class="preview-item">
              <span class="preview-label">目标平台:</span>
              <span class="preview-value">
                {{ getSelectedPlatformsText() }}
              </span>
            </div>
            <div class="preview-item">
              <span class="preview-label">预计耗时:</span>
              <span class="preview-value">{{ getEstimatedTime() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险提示 -->
      <div class="deploy-warnings" v-if="deployConfig.environment === 'production'">
        <div class="warning-header">
          <ExclamationTriangleIcon class="w-5 h-5 text-yellow-500" />
          <span class="warning-title">生产环境部署风险提示</span>
        </div>
        <ul class="warning-list">
          <li>生产环境部署可能影响用户使用，请确保在合适的时间进行</li>
          <li>建议先在测试环境验证功能正常后再部署到生产环境</li>
          <li>部署过程中可能出现短暂的服务不可用</li>
          <li>请确保已做好回滚准备，以防部署出现问题</li>
        </ul>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          取消
        </button>
        <button 
          @click="handleConfirm" 
          :disabled="!canDeploy || isDeploying"
          :class="['btn', deployConfig.environment === 'production' ? 'btn-warning' : 'btn-primary', { 'loading': isDeploying }]"
        >
          <span v-if="isDeploying">部署中...</span>
          <span v-else>
            {{ deployConfig.environment === 'production' ? '部署到生产环境' : '开始部署' }}
          </span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  RocketLaunchIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import { NotificationService } from '../../../shared/services/NotificationService';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';
import VCTextarea from '../ui/VCTextarea.vue';

// Props
interface Props {
  modelValue: boolean;
  supportedPlatforms: any[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  deploy: [config: any];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isDeploying = ref(false);

const deployConfig = ref({
  environment: 'staging',
  platforms: [] as string[],
  options: {
    skipBuild: false,
    cleanBuild: false,
    backup: true,
    verify: true,
    notify: true
  },
  notes: ''
});

// 计算属性
const canDeploy = computed(() => {
  return deployConfig.value.environment && deployConfig.value.platforms.length > 0;
});

// 方法
const getSelectedPlatformsText = () => {
  if (deployConfig.value.platforms.length === 0) {
    return '未选择';
  }
  
  const platformNames = deployConfig.value.platforms.map(key => {
    const platform = props.supportedPlatforms.find(p => p.key === key);
    return platform?.name || key;
  });
  
  return platformNames.join(', ');
};

const getEstimatedTime = () => {
  const baseTime = 2; // 基础时间2分钟
  const platformTime = deployConfig.value.platforms.length * 1.5; // 每个平台1.5分钟
  const buildTime = deployConfig.value.options.skipBuild ? 0 : 3; // 构建时间3分钟
  
  const totalMinutes = Math.ceil(baseTime + platformTime + buildTime);
  return `约 ${totalMinutes} 分钟`;
};

const getEnvClass = (environment: string) => {
  return environment === 'production' ? 'text-red-600' : 'text-yellow-600';
};

const handleConfirm = async () => {
  if (!canDeploy.value) {
    NotificationService.warning('请选择部署环境和平台');
    return;
  }
  
  // 生产环境额外确认
  if (deployConfig.value.environment === 'production') {
    const confirmed = confirm('确定要部署到生产环境吗？这可能会影响用户使用。');
    if (!confirmed) {
      return;
    }
  }
  
  emit('deploy', { ...deployConfig.value });
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  deployConfig.value = {
    environment: 'staging',
    platforms: [],
    options: {
      skipBuild: false,
      cleanBuild: false,
      backup: true,
      verify: true,
      notify: true
    },
    notes: ''
  };
};
</script>

<style scoped>
/* 部署配置样式 */
.deploy-config {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 部署目标 */
.deploy-target {
  @apply space-y-4;
}

.target-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.target-group {
  @apply space-y-3;
}

.target-label {
  @apply block text-sm font-medium text-gray-700;
}

/* 单选按钮组 */
.radio-group {
  @apply space-y-3;
}

.radio-item {
  @apply flex items-start space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50;
}

.radio-input {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

.radio-content {
  @apply flex-1;
}

.radio-label {
  @apply text-sm font-medium text-gray-900;
}

.radio-description {
  @apply block text-xs text-gray-500 mt-1;
}

/* 平台复选框 */
.platform-checkboxes {
  @apply space-y-2;
}

.platform-checkbox {
  @apply flex items-center space-x-3 cursor-pointer p-3 border border-gray-200 rounded-lg hover:bg-gray-50;
}

.checkbox-input {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.checkbox-content {
  @apply flex items-center space-x-2;
}

.platform-icon {
  @apply text-gray-600;
}

.checkbox-label {
  @apply text-sm font-medium text-gray-900;
}

/* 部署选项 */
.deploy-options {
  @apply space-y-4;
}

.options-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.option-group {
  @apply space-y-3;
}

.option-group-title {
  @apply text-sm font-medium text-gray-700;
}

.option-items {
  @apply space-y-3;
}

.option-item {
  @apply flex items-start space-x-3 cursor-pointer;
}

.option-checkbox {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.option-content {
  @apply flex-1;
}

.option-label {
  @apply text-sm font-medium text-gray-900;
}

.option-description {
  @apply block text-xs text-gray-500 mt-1;
}

/* 部署说明 */
.deploy-notes {
  @apply space-y-3;
}

/* 部署预览 */
.deploy-preview {
  @apply space-y-3;
}

.preview-card {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}

.preview-header {
  @apply flex items-center space-x-2 mb-3;
}

.preview-title {
  @apply font-medium text-blue-900;
}

.preview-content {
  @apply space-y-2;
}

.preview-item {
  @apply flex items-center justify-between text-sm;
}

.preview-label {
  @apply text-blue-700 font-medium;
}

.preview-value {
  @apply text-blue-900;
}

/* 风险提示 */
.deploy-warnings {
  @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4;
}

.warning-header {
  @apply flex items-center space-x-2 mb-3;
}

.warning-title {
  @apply text-sm font-medium text-yellow-800;
}

.warning-list {
  @apply space-y-2 text-sm text-yellow-700;
}

.warning-list li {
  @apply flex items-start;
}

.warning-list li::before {
  content: "•";
  @apply text-yellow-600 font-bold mr-2 mt-0.5 flex-shrink-0;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .target-grid {
    @apply grid-cols-1;
  }

  .options-grid {
    @apply grid-cols-1;
  }

  .preview-item {
    @apply flex-col items-start;
  }
}
</style>
