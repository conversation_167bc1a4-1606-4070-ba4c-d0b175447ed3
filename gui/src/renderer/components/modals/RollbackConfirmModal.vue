<!--
  回滚确认模态框
  提供详细的回滚信息和选项配置
-->

<template>
  <VCModal 
    v-model="visible" 
    title="确认版本回滚" 
    :loading="isValidating"
    size="large"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="rollback-confirm">
      <!-- 回滚信息 -->
      <div class="rollback-info">
        <h4 class="section-title">回滚信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">目标版本:</label>
            <span class="version-tag target-version">{{ targetVersion }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">当前版本:</label>
            <span class="version-tag current-version">{{ currentVersion }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">回滚方向:</label>
            <div class="version-flow">
              <span class="version-tag">{{ currentVersion }}</span>
              <ArrowRightIcon class="w-4 h-4 mx-2 text-gray-400" />
              <span class="version-tag">{{ targetVersion }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result">
        <h4 class="section-title">验证结果</h4>
        
        <div v-if="validationResult.valid" class="validation-success">
          <div class="success-header">
            <CheckCircleIcon class="w-5 h-5 text-green-500" />
            <span class="success-text">可以安全回滚</span>
          </div>
        </div>
        
        <div v-else class="validation-errors">
          <div class="error-header">
            <ExclamationTriangleIcon class="w-5 h-5 text-red-500" />
            <span class="error-text">发现问题，无法回滚</span>
          </div>
          <div class="error-list">
            <div v-for="issue in validationResult.issues" :key="issue" class="error-item">
              <XCircleIcon class="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
              <span>{{ issue }}</span>
            </div>
          </div>
        </div>
        
        <!-- 警告信息 -->
        <div v-if="validationResult.warnings?.length" class="validation-warnings">
          <h5 class="warnings-title">注意事项:</h5>
          <ul class="warnings-list">
            <li v-for="warning in validationResult.warnings" :key="warning" class="warning-item">
              <ExclamationTriangleIcon class="w-4 h-4 text-yellow-500 flex-shrink-0 mt-0.5" />
              <span>{{ warning }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 回滚选项 -->
      <div class="rollback-options">
        <h4 class="section-title">回滚选项</h4>
        <div class="options-grid">
          <div class="option-group">
            <h5 class="option-group-title">执行选项</h5>
            <div class="option-items">
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="options.skipBuild" 
                  class="option-checkbox"
                />
                <span class="option-label">跳过构建步骤</span>
                <span class="option-description">不重新构建，直接使用已有构建产物</span>
              </label>
              
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="options.skipDeploy" 
                  class="option-checkbox"
                />
                <span class="option-label">跳过部署步骤</span>
                <span class="option-description">只回滚代码，不更新部署环境</span>
              </label>
              
              <label class="option-item">
                <input 
                  type="checkbox" 
                  v-model="options.force" 
                  class="option-checkbox"
                />
                <span class="option-label">强制回滚</span>
                <span class="option-description">跳过额外确认，强制执行回滚</span>
              </label>
            </div>
          </div>
          
          <div class="option-group">
            <h5 class="option-group-title">目标环境</h5>
            <div class="option-items">
              <label class="radio-item">
                <input 
                  type="radio" 
                  v-model="options.environment" 
                  value="staging"
                  class="radio-input"
                />
                <span class="radio-label">测试环境</span>
                <span class="radio-description">回滚到测试环境进行验证</span>
              </label>
              
              <label class="radio-item">
                <input 
                  type="radio" 
                  v-model="options.environment" 
                  value="production"
                  class="radio-input"
                />
                <span class="radio-label">生产环境</span>
                <span class="radio-description">直接回滚到生产环境</span>
              </label>
            </div>
          </div>
          
          <div class="option-group">
            <h5 class="option-group-title">平台选择</h5>
            <div class="option-items">
              <VCSelect 
                v-model="options.platform" 
                :options="platformOptions"
                placeholder="选择目标平台（可选）"
                class="platform-select"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 回滚原因 -->
      <div class="rollback-reason">
        <h4 class="section-title">回滚原因</h4>
        <VCTextarea 
          v-model="options.reason"
          placeholder="请输入回滚原因（可选）"
          :rows="3"
          class="reason-textarea"
        />
      </div>

      <!-- 风险提示 -->
      <div class="risk-warning">
        <div class="warning-header">
          <ExclamationTriangleIcon class="w-5 h-5 text-yellow-500" />
          <span class="warning-title">风险提示</span>
        </div>
        <ul class="warning-list">
          <li>回滚操作将会改变当前的代码版本，请确保已做好备份</li>
          <li>如果跳过构建步骤，请确保目标版本的构建产物可用</li>
          <li>生产环境回滚可能影响用户使用，建议先在测试环境验证</li>
          <li>回滚后的代码变更需要重新测试和验证</li>
        </ul>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          取消
        </button>
        <button 
          @click="handleConfirm" 
          :disabled="!canConfirm"
          :class="['btn', 'btn-warning', { 'loading': isValidating }]"
        >
          <span v-if="isValidating">验证中...</span>
          <span v-else>确认回滚</span>
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowRightIcon
} from '@heroicons/vue/24/outline';

import { apiClient } from '../../../shared/api/VersionCraftAPIClient';
import { ErrorHandler } from '../../../shared/services/ErrorHandler';

// 导入基础组件（需要创建）
import VCModal from '../ui/VCModal.vue';
import VCSelect from '../ui/VCSelect.vue';
import VCTextarea from '../ui/VCTextarea.vue';

// Props
interface Props {
  modelValue: boolean;
  targetVersion: string;
  currentVersion: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [options: any];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isValidating = ref(false);
const validationResult = ref<any>(null);

const options = ref({
  skipBuild: false,
  skipDeploy: false,
  force: false,
  environment: 'staging',
  platform: '',
  reason: ''
});

const platformOptions = ref([
  { label: '全部平台', value: '' },
  { label: 'Web 平台', value: 'web-mobile' },
  { label: 'Android 平台', value: 'android' },
  { label: 'iOS 平台', value: 'ios' },
  { label: 'Windows 平台', value: 'win32' },
  { label: 'Mac 平台', value: 'mac' }
]);

// 计算属性
const canConfirm = computed(() => {
  return validationResult.value?.valid && !isValidating.value;
});

// 监听器
watch(() => props.targetVersion, async (newVersion) => {
  if (newVersion && visible.value) {
    await validateRollback(newVersion);
  }
}, { immediate: true });

watch(visible, (newVisible) => {
  if (newVisible && props.targetVersion) {
    validateRollback(props.targetVersion);
  }
});

// 方法
const validateRollback = async (version: string) => {
  if (!version) return;
  
  try {
    isValidating.value = true;
    const result = await apiClient.rollback.validate(version);
    
    if (result.success) {
      validationResult.value = result.data;
    } else {
      throw new Error(result.error || '验证失败');
    }
  } catch (error) {
    ErrorHandler.handle(error as Error, 'Validate Rollback');
    validationResult.value = {
      valid: false,
      issues: ['验证过程中发生错误'],
      warnings: []
    };
  } finally {
    isValidating.value = false;
  }
};

const handleConfirm = () => {
  if (canConfirm.value) {
    emit('confirm', { ...options.value });
  }
};

const handleCancel = () => {
  visible.value = false;
  // 重置表单
  options.value = {
    skipBuild: false,
    skipDeploy: false,
    force: false,
    environment: 'staging',
    platform: '',
    reason: ''
  };
  validationResult.value = null;
};
</script>

<style scoped>
/* 回滚确认样式 */
.rollback-confirm {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 回滚信息 */
.rollback-info {
  @apply bg-gray-50 rounded-lg p-4;
}

.info-grid {
  @apply space-y-3;
}

.info-item {
  @apply flex items-center justify-between;
}

.info-label {
  @apply text-sm font-medium text-gray-600;
}

.version-tag {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-mono font-medium;
}

.target-version {
  @apply bg-blue-100 text-blue-800;
}

.current-version {
  @apply bg-green-100 text-green-800;
}

.version-flow {
  @apply flex items-center;
}

/* 验证结果 */
.validation-result {
  @apply border rounded-lg p-4;
}

.validation-success {
  @apply border-green-200 bg-green-50;
}

.success-header {
  @apply flex items-center space-x-2;
}

.success-text {
  @apply text-green-800 font-medium;
}

.validation-errors {
  @apply border-red-200 bg-red-50;
}

.error-header {
  @apply flex items-center space-x-2 mb-3;
}

.error-text {
  @apply text-red-800 font-medium;
}

.error-list {
  @apply space-y-2;
}

.error-item {
  @apply flex items-start space-x-2 text-red-700;
}

.validation-warnings {
  @apply mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded;
}

.warnings-title {
  @apply text-sm font-medium text-yellow-800 mb-2;
}

.warnings-list {
  @apply space-y-1;
}

.warning-item {
  @apply flex items-start space-x-2 text-yellow-700 text-sm;
}

/* 回滚选项 */
.rollback-options {
  @apply border rounded-lg p-4;
}

.options-grid {
  @apply space-y-6;
}

.option-group {
  @apply space-y-3;
}

.option-group-title {
  @apply text-sm font-medium text-gray-700;
}

.option-items {
  @apply space-y-3;
}

.option-item {
  @apply flex items-start space-x-3 cursor-pointer;
}

.option-checkbox {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.option-label {
  @apply text-sm font-medium text-gray-900;
}

.option-description {
  @apply block text-xs text-gray-500 mt-1;
}

.radio-item {
  @apply flex items-start space-x-3 cursor-pointer;
}

.radio-input {
  @apply mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

.radio-label {
  @apply text-sm font-medium text-gray-900;
}

.radio-description {
  @apply block text-xs text-gray-500 mt-1;
}

.platform-select {
  @apply w-full;
}

/* 回滚原因 */
.rollback-reason {
  @apply space-y-3;
}

.reason-textarea {
  @apply w-full;
}

/* 风险提示 */
.risk-warning {
  @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4;
}

.warning-header {
  @apply flex items-center space-x-2 mb-3;
}

.warning-title {
  @apply text-sm font-medium text-yellow-800;
}

.warning-list {
  @apply space-y-2 text-sm text-yellow-700;
}

.warning-list li {
  @apply flex items-start;
}

.warning-list li::before {
  content: "•";
  @apply text-yellow-600 font-bold mr-2 mt-0.5 flex-shrink-0;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn.loading {
  @apply opacity-75 cursor-not-allowed;
}
</style>
