<!--
  部署详情模态框
  显示单个部署记录的详细信息
-->

<template>
  <VCModal 
    v-model="visible" 
    title="部署详情" 
    size="large"
    @cancel="handleCancel"
  >
    <div v-if="deployRecord" class="deploy-details">
      <!-- 基本信息 -->
      <div class="basic-info">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">部署ID:</label>
            <span class="info-value font-mono">{{ deployRecord.id }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">平台:</label>
            <span class="info-value">{{ getPlatformName(deployRecord.platform) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">环境:</label>
            <span class="info-value" :class="getEnvironmentClass(deployRecord.environment)">
              {{ deployRecord.environment === 'staging' ? '测试环境' : '生产环境' }}
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">状态:</label>
            <span :class="['info-value', deployRecord.success ? 'text-green-600' : 'text-red-600']">
              {{ deployRecord.success ? '成功' : '失败' }}
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">开始时间:</label>
            <span class="info-value">{{ formatDateTime(deployRecord.timestamp) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">耗时:</label>
            <span class="info-value">{{ formatDuration(deployRecord.duration) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">操作者:</label>
            <span class="info-value">{{ deployRecord.operator || '-' }}</span>
          </div>
          <div v-if="deployRecord.url" class="info-item">
            <label class="info-label">访问地址:</label>
            <a :href="deployRecord.url" target="_blank" class="info-value text-blue-600 hover:text-blue-800">
              {{ deployRecord.url }}
            </a>
          </div>
        </div>
      </div>

      <!-- 部署说明 -->
      <div v-if="deployRecord.notes" class="deploy-notes">
        <h4 class="section-title">部署说明</h4>
        <div class="notes-content">
          {{ deployRecord.notes }}
        </div>
      </div>

      <!-- 部署日志 -->
      <div v-if="deployRecord.logs" class="deploy-logs">
        <h4 class="section-title">部署日志</h4>
        <div class="logs-container">
          <pre class="logs-content">{{ deployRecord.logs }}</pre>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="!deployRecord.success && deployRecord.error" class="deploy-error">
        <h4 class="section-title">错误信息</h4>
        <div class="error-content">
          <ExclamationCircleIcon class="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
          <div class="error-text">
            {{ deployRecord.error }}
          </div>
        </div>
      </div>

      <!-- 部署配置 -->
      <div v-if="deployRecord.config" class="deploy-config">
        <h4 class="section-title">部署配置</h4>
        <div class="config-content">
          <pre class="config-json">{{ JSON.stringify(deployRecord.config, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 自定义按钮 -->
    <template #footer>
      <div class="modal-footer">
        <button 
          @click="handleCancel" 
          class="btn btn-secondary"
        >
          关闭
        </button>
        <button 
          v-if="deployRecord?.url"
          @click="openDeployUrl"
          class="btn btn-primary"
        >
          <LinkIcon class="w-4 h-4 mr-2" />
          访问应用
        </button>
      </div>
    </template>
  </VCModal>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  ExclamationCircleIcon,
  LinkIcon
} from '@heroicons/vue/24/outline';

// 导入基础组件
import VCModal from '../ui/VCModal.vue';

// Props
interface Props {
  modelValue: boolean;
  deployRecord: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const getPlatformName = (platform: string) => {
  const nameMap = {
    'web-mobile': 'Web 移动端',
    'android': 'Android',
    'ios': 'iOS'
  };
  return nameMap[platform as keyof typeof nameMap] || platform;
};

const getEnvironmentClass = (environment: string) => {
  return environment === 'production' ? 'text-green-600' : 'text-yellow-600';
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const formatDuration = (ms: number) => {
  if (!ms) return '-';
  
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
};

const openDeployUrl = () => {
  if (props.deployRecord?.url) {
    window.open(props.deployRecord.url, '_blank');
  }
};

const handleCancel = () => {
  visible.value = false;
};
</script>

<style scoped>
/* 部署详情样式 */
.deploy-details {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-4;
}

/* 基本信息 */
.basic-info {
  @apply space-y-4;
}

.info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.info-item {
  @apply flex flex-col space-y-1;
}

.info-label {
  @apply text-sm font-medium text-gray-600;
}

.info-value {
  @apply text-sm text-gray-900;
}

/* 部署说明 */
.deploy-notes {
  @apply space-y-3;
}

.notes-content {
  @apply p-4 bg-gray-50 rounded-lg text-sm text-gray-700;
}

/* 部署日志 */
.deploy-logs {
  @apply space-y-3;
}

.logs-container {
  @apply bg-gray-900 rounded-lg p-4 max-h-64 overflow-y-auto;
}

.logs-content {
  @apply text-sm text-green-400 font-mono whitespace-pre-wrap;
}

/* 错误信息 */
.deploy-error {
  @apply space-y-3;
}

.error-content {
  @apply flex items-start space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg;
}

.error-text {
  @apply text-sm text-red-700;
}

/* 部署配置 */
.deploy-config {
  @apply space-y-3;
}

.config-content {
  @apply bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto;
}

.config-json {
  @apply text-sm text-gray-700 font-mono whitespace-pre-wrap;
}

/* 模态框底部 */
.modal-footer {
  @apply flex items-center justify-end space-x-3;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    @apply grid-cols-1;
  }
  
  .info-item {
    @apply flex-row items-center justify-between;
  }
}
</style>
