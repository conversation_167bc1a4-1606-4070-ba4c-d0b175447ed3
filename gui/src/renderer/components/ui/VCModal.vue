<!--
  通用模态框组件
  支持不同尺寸、加载状态、自定义按钮等
-->

<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
        <div 
          :class="['modal-container', sizeClasses]"
          @click.stop
        >
          <!-- 模态框头部 -->
          <div class="modal-header">
            <h3 class="modal-title">{{ title }}</h3>
            <button 
              @click="handleClose"
              class="modal-close"
              :disabled="loading"
            >
              <XMarkIcon class="w-5 h-5" />
            </button>
          </div>

          <!-- 模态框内容 -->
          <div class="modal-body">
            <div v-if="loading" class="modal-loading">
              <div class="loading-spinner"></div>
              <p class="loading-text">{{ loadingText }}</p>
            </div>
            <div v-else class="modal-content">
              <slot />
            </div>
          </div>

          <!-- 模态框底部 -->
          <div v-if="showFooter" class="modal-footer">
            <slot name="footer">
              <div class="default-footer">
                <button 
                  @click="handleCancel"
                  class="btn btn-secondary"
                  :disabled="loading"
                >
                  {{ cancelText }}
                </button>
                <button 
                  @click="handleConfirm"
                  :class="['btn', confirmButtonClass]"
                  :disabled="loading || confirmDisabled"
                >
                  {{ confirmText }}
                </button>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { XMarkIcon } from '@heroicons/vue/24/outline';

// Props
interface Props {
  modelValue: boolean;
  title: string;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  loading?: boolean;
  loadingText?: string;
  showFooter?: boolean;
  cancelText?: string;
  confirmText?: string;
  confirmButtonClass?: string;
  confirmDisabled?: boolean;
  closeOnOverlay?: boolean;
  closeOnEscape?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  loading: false,
  loadingText: '加载中...',
  showFooter: true,
  cancelText: '取消',
  confirmText: '确认',
  confirmButtonClass: 'btn-primary',
  confirmDisabled: false,
  closeOnOverlay: true,
  closeOnEscape: true
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [];
  cancel: [];
  close: [];
}>();

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const sizeClasses = computed(() => {
  const sizeMap = {
    small: 'modal-small',
    medium: 'modal-medium',
    large: 'modal-large',
    'extra-large': 'modal-extra-large'
  };
  return sizeMap[props.size];
});

// 监听器
watch(visible, (newVisible) => {
  if (newVisible) {
    document.body.style.overflow = 'hidden';
    if (props.closeOnEscape) {
      document.addEventListener('keydown', handleEscapeKey);
    }
  } else {
    document.body.style.overflow = '';
    document.removeEventListener('keydown', handleEscapeKey);
  }
});

// 方法
const handleOverlayClick = () => {
  if (props.closeOnOverlay && !props.loading) {
    handleClose();
  }
};

const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && !props.loading) {
    handleClose();
  }
};

const handleClose = () => {
  visible.value = false;
  emit('close');
};

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

const handleConfirm = () => {
  emit('confirm');
};

// 清理
const cleanup = () => {
  document.body.style.overflow = '';
  document.removeEventListener('keydown', handleEscapeKey);
};

// 组件卸载时清理
import { onUnmounted } from 'vue';
onUnmounted(cleanup);
</script>

<style scoped>
/* 模态框遮罩 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

/* 模态框容器 */
.modal-container {
  @apply bg-white rounded-lg shadow-xl max-h-full overflow-hidden flex flex-col;
}

.modal-small {
  @apply w-full max-w-md;
}

.modal-medium {
  @apply w-full max-w-lg;
}

.modal-large {
  @apply w-full max-w-2xl;
}

.modal-extra-large {
  @apply w-full max-w-4xl;
}

/* 模态框头部 */
.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors p-1 rounded disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 模态框内容 */
.modal-body {
  @apply flex-1 overflow-y-auto;
}

.modal-loading {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.loading-text {
  @apply text-gray-600;
}

.modal-content {
  @apply p-6;
}

/* 模态框底部 */
.modal-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

.default-footer {
  @apply flex items-center justify-end space-x-3;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

/* 动画 */
.modal-enter-active,
.modal-leave-active {
  @apply transition-all duration-300 ease-out;
}

.modal-enter-from,
.modal-leave-to {
  @apply opacity-0;
}

.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  @apply transform scale-95 translate-y-4;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .modal-overlay {
    @apply p-2;
  }
  
  .modal-container {
    @apply w-full max-w-none mx-2;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    @apply px-4;
  }
  
  .modal-header {
    @apply py-4;
  }
  
  .modal-footer {
    @apply py-3;
  }
}
</style>
