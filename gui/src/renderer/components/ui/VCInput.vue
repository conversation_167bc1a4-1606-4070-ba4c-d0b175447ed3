<!--
  通用输入框组件
  支持不同类型、验证、图标等
-->

<template>
  <div class="vc-input" :class="{ 'input-disabled': disabled }">
    <label v-if="label" class="input-label" :for="inputId">
      {{ label }}
      <span v-if="required" class="required-mark">*</span>
    </label>
    
    <div class="input-container">
      <!-- 前置图标 -->
      <div v-if="prefixIcon" class="input-prefix">
        <component :is="prefixIcon" class="input-icon" />
      </div>
      
      <!-- 输入框 -->
      <input
        :id="inputId"
        ref="inputRef"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :minlength="minlength"
        :min="min"
        :max="max"
        :step="step"
        :autocomplete="autocomplete"
        :class="[
          'input-field',
          {
            'input-error': error,
            'input-success': success,
            'input-with-prefix': prefixIcon,
            'input-with-suffix': suffixIcon || showClear || showPassword
          }
        ]"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
      />
      
      <!-- 后置内容 -->
      <div v-if="suffixIcon || showClear || showPassword" class="input-suffix">
        <!-- 清除按钮 -->
        <button
          v-if="showClear && modelValue && !disabled && !readonly"
          type="button"
          class="input-action-btn"
          @click="clearInput"
        >
          <XMarkIcon class="input-icon" />
        </button>
        
        <!-- 密码显示切换 -->
        <button
          v-if="showPassword"
          type="button"
          class="input-action-btn"
          @click="togglePasswordVisibility"
        >
          <component :is="passwordVisible ? EyeSlashIcon : EyeIcon" class="input-icon" />
        </button>
        
        <!-- 后置图标 -->
        <div v-if="suffixIcon" class="input-suffix-icon">
          <component :is="suffixIcon" class="input-icon" />
        </div>
      </div>
    </div>

    <!-- 字符计数 -->
    <div v-if="showCount && maxlength" class="input-count">
      {{ (modelValue?.toString() || '').length }} / {{ maxlength }}
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="input-error-text">
      <ExclamationCircleIcon class="error-icon" />
      {{ error }}
    </div>

    <!-- 成功信息 -->
    <div v-if="success" class="input-success-text">
      <CheckCircleIcon class="success-icon" />
      {{ success }}
    </div>

    <!-- 帮助文本 -->
    <div v-if="help && !error && !success" class="input-help-text">
      {{ help }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import {
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  modelValue: string | number | null;
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search';
  label?: string;
  placeholder?: string;
  error?: string;
  success?: string;
  help?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  clearable?: boolean;
  showCount?: boolean;
  maxlength?: number;
  minlength?: number;
  min?: number;
  max?: number;
  step?: number;
  autocomplete?: string;
  prefixIcon?: any;
  suffixIcon?: any;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
  showCount: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | null];
  change: [value: string | number | null];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
  keydown: [event: KeyboardEvent];
  clear: [];
}>();

// 响应式数据
const inputRef = ref<HTMLInputElement>();
const passwordVisible = ref(false);
const isFocused = ref(false);

// 计算属性
const inputId = computed(() => {
  return `input-${Math.random().toString(36).substr(2, 9)}`;
});

const inputType = computed(() => {
  if (props.type === 'password') {
    return passwordVisible.value ? 'text' : 'password';
  }
  return props.type;
});

const showClear = computed(() => {
  return props.clearable && !props.readonly;
});

const showPassword = computed(() => {
  return props.type === 'password' && !props.readonly;
});

// 方法
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value: string | number | null = target.value;
  
  // 数字类型转换
  if (props.type === 'number' && value !== '') {
    value = Number(value);
  }
  
  emit('update:modelValue', value);
};

const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value: string | number | null = target.value;
  
  if (props.type === 'number' && value !== '') {
    value = Number(value);
  }
  
  emit('change', value);
};

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event);
};

const clearInput = () => {
  emit('update:modelValue', '');
  emit('clear');
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value;
  nextTick(() => {
    inputRef.value?.focus();
  });
};

const focus = () => {
  inputRef.value?.focus();
};

const blur = () => {
  inputRef.value?.blur();
};

const select = () => {
  inputRef.value?.select();
};

// 暴露方法
defineExpose({
  focus,
  blur,
  select
});
</script>

<style scoped>
/* 输入框容器 */
.vc-input {
  @apply relative;
}

.input-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 标签 */
.input-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.required-mark {
  @apply text-red-500 ml-1;
}

/* 输入框容器 */
.input-container {
  @apply relative flex items-center;
}

/* 前置图标 */
.input-prefix {
  @apply absolute left-3 z-10 flex items-center pointer-events-none;
}

/* 输入框 */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:cursor-not-allowed;
}

.input-field[readonly] {
  @apply bg-gray-50 cursor-default;
}

.input-field:hover:not(:disabled):not(:readonly) {
  @apply border-gray-400;
}

.input-error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.input-success {
  @apply border-green-500 focus:ring-green-500 focus:border-green-500;
}

.input-with-prefix {
  @apply pl-10;
}

.input-with-suffix {
  @apply pr-10;
}

/* 后置内容 */
.input-suffix {
  @apply absolute right-3 z-10 flex items-center space-x-1;
}

.input-action-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors p-0.5 rounded;
}

.input-suffix-icon {
  @apply flex items-center pointer-events-none;
}

/* 图标 */
.input-icon {
  @apply w-4 h-4;
}

/* 字符计数 */
.input-count {
  @apply mt-1 text-xs text-gray-500 text-right;
}

/* 错误信息 */
.input-error-text {
  @apply mt-1 flex items-center text-sm text-red-600;
}

.error-icon {
  @apply w-4 h-4 mr-1 flex-shrink-0;
}

/* 成功信息 */
.input-success-text {
  @apply mt-1 flex items-center text-sm text-green-600;
}

.success-icon {
  @apply w-4 h-4 mr-1 flex-shrink-0;
}

/* 帮助文本 */
.input-help-text {
  @apply mt-1 text-sm text-gray-500;
}

/* 特殊类型样式 */
input[type="number"] {
  @apply appearance-none;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  @apply appearance-none m-0;
}

input[type="search"] {
  @apply appearance-none;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  @apply appearance-none;
}
</style>
