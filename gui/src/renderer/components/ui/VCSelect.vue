<!--
  通用选择器组件
  支持搜索、加载状态、自定义选项等
-->

<template>
  <div class="vc-select" :class="{ 'select-disabled': disabled }">
    <label v-if="label" class="select-label">{{ label }}</label>
    
    <div class="select-container">
      <div 
        class="select-trigger"
        :class="{ 
          'select-open': isOpen, 
          'select-error': error,
          'select-loading': loading 
        }"
        @click="toggleDropdown"
      >
        <div class="select-value">
          <span v-if="selectedOption" class="selected-text">
            {{ selectedOption.label }}
          </span>
          <span v-else class="placeholder-text">
            {{ placeholder }}
          </span>
        </div>
        
        <div class="select-icons">
          <div v-if="loading" class="loading-spinner"></div>
          <ChevronDownIcon 
            v-else
            class="select-arrow"
            :class="{ 'arrow-up': isOpen }"
          />
        </div>
      </div>

      <!-- 下拉选项 -->
      <Transition name="dropdown">
        <div v-if="isOpen" class="select-dropdown">
          <!-- 搜索框 -->
          <div v-if="searchable" class="search-container">
            <div class="search-input-wrapper">
              <MagnifyingGlassIcon class="search-icon" />
              <input
                ref="searchInput"
                v-model="searchQuery"
                type="text"
                class="search-input"
                placeholder="搜索选项..."
                @keydown.escape="closeDropdown"
                @keydown.enter.prevent="selectFirstFiltered"
                @keydown.up.prevent="navigateUp"
                @keydown.down.prevent="navigateDown"
              />
            </div>
          </div>

          <!-- 选项列表 -->
          <div class="options-container">
            <div v-if="filteredOptions.length === 0" class="no-options">
              <span v-if="loading">加载中...</span>
              <span v-else-if="searchQuery">未找到匹配选项</span>
              <span v-else>暂无选项</span>
            </div>
            
            <div
              v-for="(option, index) in filteredOptions"
              :key="option.value"
              class="select-option"
              :class="{ 
                'option-selected': option.value === modelValue,
                'option-highlighted': index === highlightedIndex,
                'option-disabled': option.disabled
              }"
              @click="selectOption(option)"
              @mouseenter="highlightedIndex = index"
            >
              <div class="option-content">
                <span class="option-label">{{ option.label }}</span>
                <span v-if="option.description" class="option-description">
                  {{ option.description }}
                </span>
              </div>
              
              <CheckIcon 
                v-if="option.value === modelValue" 
                class="option-check"
              />
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="select-error-text">
      {{ error }}
    </div>

    <!-- 帮助文本 -->
    <div v-if="help" class="select-help-text">
      {{ help }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import {
  ChevronDownIcon,
  MagnifyingGlassIcon,
  CheckIcon
} from '@heroicons/vue/24/outline';

// 选项接口
interface SelectOption {
  label: string;
  value: string | number;
  description?: string;
  disabled?: boolean;
}

// Props
interface Props {
  modelValue: string | number | null;
  options: SelectOption[];
  label?: string;
  placeholder?: string;
  error?: string;
  help?: string;
  disabled?: boolean;
  loading?: boolean;
  searchable?: boolean;
  clearable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
  disabled: false,
  loading: false,
  searchable: false,
  clearable: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | null];
  change: [option: SelectOption | null];
  search: [query: string];
}>();

// 响应式数据
const isOpen = ref(false);
const searchQuery = ref('');
const highlightedIndex = ref(-1);
const searchInput = ref<HTMLInputElement>();

// 计算属性
const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue) || null;
});

const filteredOptions = computed(() => {
  if (!props.searchable || !searchQuery.value) {
    return props.options;
  }
  
  const query = searchQuery.value.toLowerCase();
  return props.options.filter(option => 
    option.label.toLowerCase().includes(query) ||
    option.description?.toLowerCase().includes(query)
  );
});

// 监听器
watch(isOpen, async (newIsOpen) => {
  if (newIsOpen) {
    highlightedIndex.value = -1;
    if (props.searchable) {
      await nextTick();
      searchInput.value?.focus();
    }
    document.addEventListener('click', handleClickOutside);
  } else {
    searchQuery.value = '';
    document.removeEventListener('click', handleClickOutside);
  }
});

watch(() => props.modelValue, () => {
  // 当值改变时，重置高亮索引
  highlightedIndex.value = -1;
});

// 方法
const toggleDropdown = () => {
  if (props.disabled || props.loading) return;
  isOpen.value = !isOpen.value;
};

const closeDropdown = () => {
  isOpen.value = false;
};

const selectOption = (option: SelectOption) => {
  if (option.disabled) return;
  
  emit('update:modelValue', option.value);
  emit('change', option);
  closeDropdown();
};

const selectFirstFiltered = () => {
  if (filteredOptions.value.length > 0) {
    selectOption(filteredOptions.value[0]);
  }
};

const navigateUp = () => {
  if (highlightedIndex.value > 0) {
    highlightedIndex.value--;
  } else {
    highlightedIndex.value = filteredOptions.value.length - 1;
  }
};

const navigateDown = () => {
  if (highlightedIndex.value < filteredOptions.value.length - 1) {
    highlightedIndex.value++;
  } else {
    highlightedIndex.value = 0;
  }
};

const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (!target.closest('.vc-select')) {
    closeDropdown();
  }
};

// 搜索处理
watch(searchQuery, (newQuery) => {
  emit('search', newQuery);
  highlightedIndex.value = -1;
});

// 清理
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* 选择器容器 */
.vc-select {
  @apply relative;
}

.select-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 标签 */
.select-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 选择器容器 */
.select-container {
  @apply relative;
}

/* 触发器 */
.select-trigger {
  @apply w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left cursor-pointer transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.select-trigger:hover {
  @apply border-gray-400;
}

.select-open {
  @apply border-blue-500 ring-2 ring-blue-500;
}

.select-error {
  @apply border-red-500 ring-2 ring-red-500;
}

.select-loading {
  @apply cursor-wait;
}

.select-value {
  @apply flex items-center justify-between;
}

.selected-text {
  @apply text-gray-900;
}

.placeholder-text {
  @apply text-gray-500;
}

/* 图标 */
.select-icons {
  @apply flex items-center;
}

.loading-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600;
}

.select-arrow {
  @apply w-4 h-4 text-gray-400 transition-transform;
}

.arrow-up {
  @apply transform rotate-180;
}

/* 下拉菜单 */
.select-dropdown {
  @apply absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-hidden;
}

/* 搜索容器 */
.search-container {
  @apply p-2 border-b border-gray-200;
}

.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400;
}

.search-input {
  @apply w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* 选项容器 */
.options-container {
  @apply max-h-48 overflow-y-auto;
}

.no-options {
  @apply px-3 py-2 text-sm text-gray-500 text-center;
}

/* 选项 */
.select-option {
  @apply px-3 py-2 cursor-pointer transition-colors flex items-center justify-between;
}

.select-option:hover,
.option-highlighted {
  @apply bg-gray-100;
}

.option-selected {
  @apply bg-blue-50 text-blue-700;
}

.option-disabled {
  @apply opacity-50 cursor-not-allowed;
}

.option-content {
  @apply flex-1 min-w-0;
}

.option-label {
  @apply block text-sm text-gray-900 truncate;
}

.option-description {
  @apply block text-xs text-gray-500 truncate;
}

.option-check {
  @apply w-4 h-4 text-blue-600 flex-shrink-0;
}

/* 错误和帮助文本 */
.select-error-text {
  @apply mt-1 text-sm text-red-600;
}

.select-help-text {
  @apply mt-1 text-sm text-gray-500;
}

/* 下拉动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  @apply transition-all duration-200 ease-out;
}

.dropdown-enter-from,
.dropdown-leave-to {
  @apply opacity-0 transform scale-95 -translate-y-2;
}
</style>
