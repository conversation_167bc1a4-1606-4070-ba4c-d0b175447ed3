import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { IPC_COMMANDS } from '../../shared/constants/ipc-commands';

export const useAppStore = defineStore('app', () => {
  // 状态
  const currentProject = ref<string | null>(null);
  const projectInfo = ref<any>(null);
  const currentVersion = ref<any>(null);
  const versionHistory = ref<any[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 构建状态
  const isBuilding = ref(false);
  const currentBuild = ref<any>(null);
  const buildLogs = ref<string[]>([]);
  const buildHistory = ref<any[]>([]);

  // 计算属性
  const hasProject = computed(() => !!currentProject.value);
  const projectName = computed(() => {
    if (!currentProject.value) return '';
    return currentProject.value.split(/[/\\]/).pop() || '';
  });
  const buildProgress = computed(() => currentBuild.value?.progress || 0);
  const buildStatus = computed(() => currentBuild.value?.status || 'idle');

  // 操作方法
  const setCurrentProject = async (projectPath: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      currentProject.value = projectPath;

      // 检查 electronAPI 是否可用
      if (!window.electronAPI) {
        console.warn('electronAPI 不可用，使用模拟数据');
        // 设置模拟数据
        projectInfo.value = {
          name: 'Demo Project',
          path: projectPath,
          package: { name: 'demo-project', version: '1.0.0' }
        };
        currentVersion.value = { version: '1.0.0', lastModified: new Date().toISOString() };
        versionHistory.value = [
          { version: '1.0.0', date: new Date().toISOString(), message: '初始版本' }
        ];
        return;
      }

      // 加载项目信息
      await loadProjectInfo();
      await loadCurrentVersion();
      await loadVersionHistory();

    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载项目失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const loadProjectInfo = async () => {
    if (!currentProject.value) return;

    try {
      if (!window.electronAPI) {
        console.warn('electronAPI 不可用，跳过加载项目信息');
        return;
      }

      const result = await window.electronAPI.getProjectInfo();
      if (result.success) {
        projectInfo.value = result.data;
      } else {
        throw new Error(result.error || '获取项目信息失败');
      }
    } catch (err) {
      console.error('Load project info error:', err);
      throw err;
    }
  };

  const loadCurrentVersion = async () => {
    if (!currentProject.value) return;
    
    try {
      if (!window.electronAPI) {
        console.warn('electronAPI 不可用，跳过加载项目信息');
        return;
      }

      const result = await window.electronAPI.getCurrentVersion();
      if (result.success) {
        currentVersion.value = result.data;
      } else {
        throw new Error(result.error || '获取当前版本失败');
      }
    } catch (err) {
      console.error('Load current version error:', err);
      throw err;
    }
  };

  const loadVersionHistory = async () => {
    if (!currentProject.value) return;
    
    try {
      if (!window.electronAPI) {
        console.warn('electronAPI 不可用，跳过加载项目信息');
        return;
      }

      const result = await window.electronAPI.getVersionHistory();
      if (result.success) {
        versionHistory.value = result.data || [];
      } else {
        throw new Error(result.error || '获取版本历史失败');
      }
    } catch (err) {
      console.error('Load version history error:', err);
      throw err;
    }
  };

  const bumpVersion = async (options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }) => {
    console.log('🚀 [Store] 开始版本升级:', options);

    if (!currentProject.value) {
      console.error('❌ [Store] 版本升级失败: 未选择项目');
      throw new Error('未选择项目');
    }

    if (!window.electronAPI) {
      console.error('❌ [Store] 版本升级失败: electronAPI 不可用');
      throw new Error('electronAPI 不可用');
    }

    try {
      isLoading.value = true;
      error.value = null;

      console.log('📡 [Store] 调用 electronAPI.bumpVersion...');
      const result = await window.electronAPI.bumpVersion(options);
      console.log('📡 [Store] electronAPI.bumpVersion 返回:', result);

      if (result.success) {
        console.log('✅ [Store] 版本升级成功，刷新数据...');
        // 刷新版本信息
        await loadCurrentVersion();
        await loadVersionHistory();
        return result.data;
      } else {
        console.error('❌ [Store] 版本升级失败:', result.error);
        throw new Error(result.error || '版本升级失败');
      }
    } catch (err) {
      console.error('💥 [Store] 版本升级异常:', err);
      error.value = err instanceof Error ? err.message : '版本升级失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const rollbackVersion = async (targetVersion: string, force: boolean = false) => {
    if (!currentProject.value) {
      throw new Error('未选择项目');
    }

    try {
      isLoading.value = true;
      error.value = null;

      if (!window.electronAPI) {
        console.warn('electronAPI 不可用，跳过加载项目信息');
        return;
      }

      const result = await window.electronAPI.rollbackVersion(targetVersion, force);
      
      if (result.success) {
        // 刷新版本信息
        await loadCurrentVersion();
        await loadVersionHistory();
        return result.data;
      } else {
        throw new Error(result.error || '版本回滚失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '版本回滚失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const startBuild = async (platform: string, options: any = {}) => {
    if (!currentProject.value) {
      throw new Error('未选择项目');
    }

    try {
      // 重置构建状态
      isBuilding.value = true;
      buildLogs.value = [];
      currentBuild.value = null;
      error.value = null;

      if (!window.electronAPI) {
        throw new Error('Electron API 不可用，无法启动构建');
      }

      // 调用主进程开始构建 - 使用新的命名空间
      const result = await window.electronAPI.invoke(IPC_COMMANDS.BUILD.START, platform, options);

      if (result.success) {
        // 创建构建记录
        currentBuild.value = {
          id: result.data.buildId,
          platform: platform,
          version: currentVersion.value?.version || '1.0.0',
          status: 'running',
          progress: 0,
          startTime: result.data.startTime || new Date().toISOString(),
          currentStep: '初始化构建...'
        };

        // 开始监听构建进度
        setupBuildEventListeners(result.data.buildId);

        return result.data;
      } else {
        throw new Error(result.error || '启动构建失败');
      }
    } catch (err) {
      // 构建启动失败，重置状态
      isBuilding.value = false;
      currentBuild.value = null;
      error.value = err instanceof Error ? err.message : '启动构建失败';
      throw err;
    }
  };

  // 设置构建事件监听器
  const setupBuildEventListeners = (buildId: string) => {
    if (!window.electronAPI) return;

    // 监听构建进度事件
    window.electronAPI.on(IPC_COMMANDS.EVENTS.BUILD_PROGRESS, (data: any) => {
      if (data.buildId === buildId && currentBuild.value) {
        currentBuild.value.progress = data.progress || 0;
        currentBuild.value.currentStep = data.step || '构建中...';

        // 添加构建日志
        if (data.message) {
          const timestamp = new Date().toLocaleTimeString();
          buildLogs.value.push(`[${timestamp}] ${data.message}`);
        }
      }
    });

    // 监听构建完成事件
    window.electronAPI.on(IPC_COMMANDS.EVENTS.BUILD_COMPLETE, (data: any) => {
      if (data.buildId === buildId && currentBuild.value) {
        const endTime = new Date().toISOString();
        const duration = Date.now() - new Date(currentBuild.value.startTime).getTime();

        currentBuild.value.status = data.success ? 'completed' : 'failed';
        currentBuild.value.progress = 100;
        currentBuild.value.endTime = endTime;
        currentBuild.value.duration = duration;
        currentBuild.value.error = data.error;

        isBuilding.value = false;

        // 添加最终日志
        const timestamp = new Date().toLocaleTimeString();
        if (data.success) {
          buildLogs.value.push(`[${timestamp}] [SUCCESS] 构建成功完成`);
          if (data.outputPath) {
            buildLogs.value.push(`[${timestamp}] [INFO] 输出路径: ${data.outputPath}`);
          }
          if (data.buildSize) {
            buildLogs.value.push(`[${timestamp}] [INFO] 构建大小: ${data.buildSize}`);
          }
        } else {
          buildLogs.value.push(`[${timestamp}] [ERROR] 构建失败: ${data.error || '未知错误'}`);
        }

        // 添加到历史记录
        addToBuildHistory(currentBuild.value);
      }
    });

    // 监听构建日志事件
    window.electronAPI.on(IPC_COMMANDS.EVENTS.BUILD_LOG, (data: any) => {
      if (data.buildId === buildId) {
        const timestamp = new Date().toLocaleTimeString();
        const level = data.level || 'INFO';
        buildLogs.value.push(`[${timestamp}] [${level}] ${data.message}`);
      }
    });
  };

  // 添加到构建历史
  const addToBuildHistory = (build: any) => {
    buildHistory.value.unshift({
      id: build.id,
      platform: build.platform,
      version: build.version,
      status: build.status === 'completed' ? 'success' : 'failed',
      startTime: build.startTime,
      endTime: build.endTime || new Date().toISOString(),
      duration: build.duration || (Date.now() - new Date(build.startTime).getTime()),
      buildSize: build.buildSize,
      outputPath: build.outputPath,
      error: build.error
    });

    // 限制历史记录数量
    if (buildHistory.value.length > 50) {
      buildHistory.value = buildHistory.value.slice(0, 50);
    }
  };

  // 取消构建
  const cancelBuild = async () => {
    if (!currentBuild.value || !window.electronAPI) {
      return;
    }

    try {
      const result = await window.electronAPI.invoke(IPC_COMMANDS.BUILD.CANCEL, currentBuild.value.id);

      if (result.success) {
        currentBuild.value.status = 'cancelled';
        currentBuild.value.endTime = new Date().toISOString();
        currentBuild.value.duration = Date.now() - new Date(currentBuild.value.startTime).getTime();

        const timestamp = new Date().toLocaleTimeString();
        buildLogs.value.push(`[${timestamp}] [WARN] 构建已被用户取消`);

        // 添加到历史记录
        addToBuildHistory(currentBuild.value);
      }
    } catch (err) {
      console.error('取消构建失败:', err);
      error.value = '取消构建失败';
    } finally {
      isBuilding.value = false;
    }
  };

  // 清除当前构建
  const clearCurrentBuild = () => {
    currentBuild.value = null;
    buildLogs.value = [];
  };

  const refreshAll = async () => {
    if (!currentProject.value) return;
    
    try {
      isLoading.value = true;
      await Promise.all([
        loadProjectInfo(),
        loadCurrentVersion(),
        loadVersionHistory()
      ]);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刷新数据失败';
    } finally {
      isLoading.value = false;
    }
  };

  const refreshCurrentVersion = async () => {
    if (currentProject.value) {
      await loadCurrentVersion();
    }
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    currentProject.value = null;
    projectInfo.value = null;
    currentVersion.value = null;
    versionHistory.value = [];
    error.value = null;
    isLoading.value = false;
  };

  return {
    // 状态
    currentProject,
    projectInfo,
    currentVersion,
    versionHistory,
    isLoading,
    error,

    // 构建状态
    isBuilding,
    currentBuild,
    buildLogs,
    buildHistory,

    // 计算属性
    hasProject,
    projectName,
    buildProgress,
    buildStatus,

    // 操作方法
    setCurrentProject,
    loadProjectInfo,
    loadCurrentVersion,
    loadVersionHistory,
    bumpVersion,
    rollbackVersion,
    startBuild,
    cancelBuild,
    clearCurrentBuild,
    refreshAll,
    refreshCurrentVersion,
    clearError,
    reset
  };
});
