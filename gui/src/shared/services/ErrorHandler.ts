/**
 * 统一错误处理服务
 * 提供错误分类、日志记录、用户通知等功能
 */

import { NotificationService } from './NotificationService';
import { Logger } from '../utils/Logger';
import { getUserAgent, getCurrentURL } from '../utils/environment';
import { APIError } from '../types/api';

// ==================== 错误类型定义 ====================

export type ErrorType = 
  | 'NETWORK'      // 网络错误
  | 'VALIDATION'   // 验证错误
  | 'PERMISSION'   // 权限错误
  | 'API'          // API 错误
  | 'SYSTEM'       // 系统错误
  | 'USER'         // 用户操作错误
  | 'CRITICAL'     // 严重错误
  | 'UNKNOWN';     // 未知错误

export interface ErrorHandleOptions {
  showNotification?: boolean;
  logError?: boolean;
  reportError?: boolean;
  context?: Record<string, any>;
  // Vue 组件错误相关
  componentName?: string;
  componentInfo?: string;
  instance?: string;
  errorInfo?: string;
  // Promise 错误相关
  reason?: any;
  promise?: Promise<any>;
  // 文件错误相关
  filename?: string;
  lineno?: number;
  colno?: number;
}

export interface NotificationConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary';
}

// ==================== 自定义错误类 ====================

export class NetworkError extends Error {
  constructor(message: string, public url?: string, public status?: number) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public field?: string, public value?: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class PermissionError extends Error {
  constructor(message: string, public requiredPermission?: string) {
    super(message);
    this.name = 'PermissionError';
  }
}

export class SystemError extends Error {
  constructor(message: string, public systemInfo?: any) {
    super(message);
    this.name = 'SystemError';
  }
}

// ==================== 错误处理服务 ====================

class ErrorHandler {
  private static errorCounts = new Map<string, number>();
  private static lastErrors = new Map<string, number>();

  /**
   * 主要的错误处理方法
   */
  static handle(
    error: Error | APIError | any, 
    context: string, 
    options: ErrorHandleOptions = {}
  ): void {
    const {
      showNotification = true,
      logError = true,
      reportError = false,
      context: additionalContext = {}
    } = options;

    try {
      // 1. 错误分类
      const errorType = this.classifyError(error);
      
      // 2. 防重复处理
      if (this.isDuplicateError(error, context)) {
        return;
      }

      // 3. 日志记录
      if (logError) {
        this.logError(error, context, errorType, additionalContext);
      }

      // 4. 用户通知
      if (showNotification) {
        const notification = this.createUserNotification(error, errorType, context);
        NotificationService.show(notification);
      }

      // 5. 错误上报（严重错误）
      if (reportError || errorType === 'CRITICAL') {
        this.reportError(error, context, errorType, additionalContext);
      }

      // 6. 更新错误统计
      this.updateErrorStats(error, context);

    } catch (handlingError) {
      // 错误处理过程中的错误，记录到控制台
      console.error('Error in ErrorHandler.handle:', handlingError);
      console.error('Original error:', error);
    }
  }

  /**
   * 快捷方法：处理 API 错误
   */
  static handleAPIError(error: APIError, context: string): void {
    this.handle(error, context, {
      showNotification: true,
      logError: true,
      reportError: error.code === 'CRITICAL'
    });
  }

  /**
   * 快捷方法：处理网络错误
   */
  static handleNetworkError(error: NetworkError, context: string): void {
    this.handle(error, context, {
      showNotification: true,
      logError: true,
      reportError: false
    });
  }

  /**
   * 快捷方法：处理验证错误
   */
  static handleValidationError(error: ValidationError, context: string): void {
    this.handle(error, context, {
      showNotification: true,
      logError: false,
      reportError: false
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 错误分类
   */
  private static classifyError(error: any): ErrorType {
    if (error instanceof NetworkError) return 'NETWORK';
    if (error instanceof ValidationError) return 'VALIDATION';
    if (error instanceof PermissionError) return 'PERMISSION';
    if (error instanceof SystemError) return 'SYSTEM';
    
    // 基于错误消息的分类
    const message = error?.message?.toLowerCase() || '';
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return 'NETWORK';
    }
    
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return 'PERMISSION';
    }
    
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return 'VALIDATION';
    }
    
    if (message.includes('critical') || message.includes('fatal') || message.includes('crash')) {
      return 'CRITICAL';
    }
    
    if (error instanceof APIError || error?.name === 'APIError') {
      return 'API';
    }
    
    return 'UNKNOWN';
  }

  /**
   * 检查是否为重复错误
   */
  private static isDuplicateError(error: any, context: string): boolean {
    const errorKey = `${context}:${error?.message || 'unknown'}`;
    const now = Date.now();
    const lastTime = this.lastErrors.get(errorKey) || 0;
    
    // 5秒内的相同错误视为重复
    if (now - lastTime < 5000) {
      return true;
    }
    
    this.lastErrors.set(errorKey, now);
    return false;
  }

  /**
   * 记录错误日志
   */
  private static logError(
    error: any, 
    context: string, 
    errorType: ErrorType, 
    additionalContext: Record<string, any>
  ): void {
    const logData = {
      error: {
        name: error?.name || 'Unknown',
        message: error?.message || 'Unknown error',
        stack: error?.stack,
        code: error?.code
      },
      context,
      errorType,
      timestamp: new Date().toISOString(),
      userAgent: getUserAgent(),
      url: getCurrentURL(),
      additionalContext
    };

    // 根据错误类型选择日志级别
    switch (errorType) {
      case 'CRITICAL':
        Logger.error(`[CRITICAL] ${context}`, logData);
        break;
      case 'SYSTEM':
      case 'API':
        Logger.error(`[${errorType}] ${context}`, logData);
        break;
      case 'NETWORK':
      case 'PERMISSION':
        Logger.warn(`[${errorType}] ${context}`, logData);
        break;
      case 'VALIDATION':
      case 'USER':
        Logger.info(`[${errorType}] ${context}`, logData);
        break;
      default:
        Logger.error(`[UNKNOWN] ${context}`, logData);
    }
  }

  /**
   * 创建用户通知
   */
  private static createUserNotification(
    error: any,
    errorType: ErrorType,
    context: string
  ): NotificationConfig {
    // {{ 为系统错误使用专门的对话框 }}
    // 检查是否为 Electron API 不可用错误
    if (errorType === 'SYSTEM' && error.message && error.message.includes('Electron API 不可用')) {
      // 使用系统错误对话框而不是普通通知
      this.showSystemErrorDialog(error, context);
    }

    const config: NotificationConfig = {
      type: 'error',
      title: this.getErrorTitle(errorType),
      message: this.getUserFriendlyMessage(error, errorType),
      duration: errorType === 'CRITICAL' ? 0 : 5000, // 严重错误不自动消失
      actions: this.getErrorActions(error, errorType, context)
    };

    return config;
  }

  /**
   * {{ 显示系统错误对话框 }}
   * 显示系统错误对话框
   */
  private static showSystemErrorDialog(error: any, context: string): void {
    // 通过事件系统显示系统错误对话框
    const event = new CustomEvent('show-system-error', {
      detail: {
        title: 'Electron API 不可用',
        message: '应用程序无法正常连接到系统服务，这可能是由于应用程序未在正确的环境中运行导致的。',
        details: JSON.stringify({
          error: error.message,
          context,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        }, null, 2),
        suggestions: [
          '请确保应用程序正确启动',
          '尝试重新启动应用程序',
          '检查应用程序是否在 Electron 环境中运行',
          '清除应用程序缓存后重试',
          '如果问题持续存在，请联系技术支持'
        ]
      }
    });

    window.dispatchEvent(event);
  }

  /**
   * 获取错误标题
   */
  private static getErrorTitle(errorType: ErrorType): string {
    const titles: Record<ErrorType, string> = {
      NETWORK: '网络错误',
      VALIDATION: '输入错误',
      PERMISSION: '权限错误',
      API: '服务错误',
      SYSTEM: '系统错误',
      USER: '操作错误',
      CRITICAL: '严重错误',
      UNKNOWN: '未知错误'
    };
    
    return titles[errorType] || '错误';
  }

  /**
   * 获取用户友好的错误消息
   */
  private static getUserFriendlyMessage(error: any, errorType: ErrorType): string {
    const message = error?.message || '发生了未知错误';
    
    // 根据错误类型提供更友好的消息
    switch (errorType) {
      case 'NETWORK':
        return '网络连接失败，请检查网络设置后重试';
      case 'VALIDATION':
        return `输入验证失败：${message}`;
      case 'PERMISSION':
        return '您没有执行此操作的权限，请联系管理员';
      case 'API':
        return `服务调用失败：${message}`;
      case 'SYSTEM':
        return '系统出现异常，请稍后重试';
      case 'CRITICAL':
        return `发生严重错误：${message}，请重启应用`;
      default:
        return message;
    }
  }

  /**
   * 获取错误操作按钮
   */
  private static getErrorActions(
    error: any, 
    errorType: ErrorType, 
    context: string
  ): NotificationAction[] {
    const actions: NotificationAction[] = [];

    // 重试按钮（适用于网络和API错误）
    if (errorType === 'NETWORK' || errorType === 'API') {
      actions.push({
        label: '重试',
        action: () => {
          // 这里可以实现重试逻辑
          console.log('Retry action for:', context);
        },
        variant: 'primary'
      });
    }

    // 报告问题按钮（严重错误）
    if (errorType === 'CRITICAL' || errorType === 'SYSTEM') {
      actions.push({
        label: '报告问题',
        action: () => {
          this.reportError(error, context, errorType, {});
        },
        variant: 'secondary'
      });
    }

    return actions;
  }

  /**
   * 上报错误
   */
  private static reportError(
    error: any, 
    context: string, 
    errorType: ErrorType, 
    additionalContext: Record<string, any>
  ): void {
    // 这里可以实现错误上报逻辑
    // 例如发送到错误监控服务
    console.log('Error reported:', {
      error,
      context,
      errorType,
      additionalContext,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 更新错误统计
   */
  private static updateErrorStats(error: any, context: string): void {
    const errorKey = `${context}:${error?.name || 'Unknown'}`;
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);
  }

  /**
   * 获取错误统计
   */
  static getErrorStats(): Map<string, number> {
    return new Map(this.errorCounts);
  }

  /**
   * 清除错误统计
   */
  static clearErrorStats(): void {
    this.errorCounts.clear();
    this.lastErrors.clear();
  }
}

// 导出
export { ErrorHandler };
export default ErrorHandler;
