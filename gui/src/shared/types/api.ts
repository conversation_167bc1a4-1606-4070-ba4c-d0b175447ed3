/**
 * API 响应和请求类型定义
 * 统一的 IPC 通信类型系统
 */

// ==================== 基础 API 类型 ====================

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  timestamp?: string;
}

export class APIError extends Error {
  constructor(
    public channel: string,
    public originalError: any,
    public code?: string
  ) {
    super(`API Error on channel ${channel}: ${originalError?.message || originalError}`);
    this.name = 'APIError';
  }
}

// ==================== 版本管理类型 ====================

export interface BumpOptions {
  type: 'major' | 'minor' | 'patch';
  prerelease?: string;
  message?: string;
  createTag?: boolean;
  generateChangelog?: boolean;
}

export interface TagOptions {
  version?: string;
  message?: string;
  pushToRemote?: boolean;
}

export interface ChangelogOptions {
  from?: string;
  to?: string;
  format?: 'markdown' | 'json';
  includeCommits?: boolean;
}

export interface ReleaseOptions {
  createTag?: boolean;
  generateChangelog?: boolean;
  pushToRemote?: boolean;
}

export interface VersionInfo {
  version: string;
  name?: string;
  description?: string;
  author?: string;
  lastModified: string;
  gitInfo?: {
    branch: string;
    commit: string;
    tag?: string;
  };
}

export interface VersionHistoryItem {
  version: string;
  message: string;
  date: string;
  author: string;
  isCurrent: boolean;
  commitHash?: string;
  tag?: string;
}

// ==================== 构建管理类型 ====================

export interface BuildOptions {
  buildId?: string;
  clean?: boolean;
  sign?: boolean;
  platform?: string;
  configuration?: 'debug' | 'release';
  outputPath?: string;
}

export interface BuildResult {
  success: boolean;
  buildId: string;
  platform: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  outputPath?: string;
  fileSize?: number;
  error?: string;
  logs?: string[];
}

export interface BuildStats {
  totalBuilds: number;
  successfulBuilds: number;
  failedBuilds: number;
  averageBuildTime: number;
  lastBuildTime?: string;
  platforms: {
    [platform: string]: {
      builds: number;
      successRate: number;
      averageTime: number;
    };
  };
}

export interface BuildHistoryItem {
  id: string;
  platform: string;
  version: string;
  status: 'success' | 'failed' | 'running' | 'cancelled';
  startTime: string;
  duration?: number;
  buildSize?: string;
  error?: string;
}

// ==================== 回滚管理类型 ====================

export interface RollbackOptions {
  force?: boolean;
  skipBuild?: boolean;
  skipDeploy?: boolean;
  platform?: string;
  environment?: 'staging' | 'production';
  reason?: string;
}

export interface RollbackInfo {
  version: string;
  date: string;
  commitHash?: string;
  buildExists: boolean;
  tagExists: boolean;
  description?: string;
}

export interface RollbackValidation {
  valid: boolean;
  issues: string[];
  warnings: string[];
  currentVersion?: string;
  targetVersion: string;
}

export interface RollbackResult {
  success: boolean;
  fromVersion: string;
  toVersion: string;
  timestamp: string;
  reason?: string;
  error?: string;
}

export interface RollbackRecord {
  id: string;
  fromVersion: string;
  toVersion: string;
  timestamp: string;
  success: boolean;
  reason?: string;
  rollbackBy?: string;
  error?: string;
  duration?: number;
}

export interface CheckpointResult {
  checkpointName: string;
  version: string;
  tag: string;
  timestamp: string;
}

// ==================== 热更新管理类型 ====================

export interface ManifestOptions {
  version?: string;
  platform?: string;
  outputPath?: string;
  includeNative?: boolean;
  compressionLevel?: number;
}

export interface PatchOptions {
  fromVersion: string;
  toVersion: string;
  outputPath?: string;
  compressionLevel?: number;
}

export interface VerifyOptions {
  manifestPath: string;
  checkFiles?: boolean;
  checkChecksums?: boolean;
}

export interface CleanOptions {
  keepCount?: number;
  dryRun?: boolean;
  distDir?: string;
}

export interface CompareOptions {
  version1: string;
  version2: string;
  detailed?: boolean;
}

export interface HotUpdateResult {
  success: boolean;
  message: string;
  data?: any;
  stats?: {
    totalFiles?: number;
    totalSize?: number;
    addedFiles?: number;
    modifiedFiles?: number;
    deletedFiles?: number;
  };
}

export interface ResourceInfo {
  path: string;
  size: number;
  checksum: string;
  type: 'script' | 'scene' | 'texture' | 'audio' | 'other';
}

export interface ManifestInfo {
  version: string;
  buildNumber: string;
  releaseDate: string;
  totalSize: number;
  mandatory: boolean;
  resources: ResourceInfo[];
}

// ==================== 部署管理类型 ====================

export interface DeployOptions {
  platform?: string;
  environment: 'staging' | 'production';
  skipBuild?: boolean;
  rollbackOnFailure?: boolean;
  notifyOnComplete?: boolean;
}

export interface DeployResult {
  success: boolean;
  deploymentId: string;
  environment: string;
  platform?: string;
  version: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  url?: string;
  error?: string;
}

export interface DeploymentRecord {
  id: string;
  version: string;
  environment: string;
  platform?: string;
  status: 'success' | 'failed' | 'running' | 'rolled-back';
  timestamp: string;
  duration?: number;
  deployedBy?: string;
  url?: string;
  error?: string;
}

export interface DeploymentStatus {
  currentDeployments: {
    staging?: DeploymentRecord;
    production?: DeploymentRecord;
  };
  recentDeployments: DeploymentRecord[];
  statistics: {
    totalDeployments: number;
    successfulDeployments: number;
    failedDeployments: number;
    averageDeployTime: number;
  };
}

// ==================== 配置管理类型 ====================

export interface ConfigValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ConfigExportOptions {
  filePath: string;
  format?: 'json' | 'yaml';
  includeSecrets?: boolean;
}

export interface ConfigImportOptions {
  filePath: string;
  merge?: boolean;
  validateOnly?: boolean;
}

// ==================== 通用工具类型 ====================

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterOptions {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string[];
  platform?: string[];
}

export interface ListResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
