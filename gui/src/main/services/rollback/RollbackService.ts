import { RollbackManager, RollbackInfo, RollbackOptions, RollbackRecord } from '@version-craft/core';

/**
 * 回滚管理服务
 * 基于 RollbackManager 核心模块的完整功能实现
 * 
 * 核心功能：
 * 1. getRollbackVersions - 获取可回滚版本列表
 * 2. rollbackToVersion - 回滚到指定版本
 * 3. rollbackToLastVersion - 回滚到上一版本
 * 4. getRollbackStatus - 获取回滚状态
 * 5. createRollbackCheckpoint - 创建回滚检查点
 * 6. getRollbackHistory - 获取回滚历史
 * 7. validateRollback - 验证回滚可行性
 */
export class RollbackService {
  private rollbackManager: RollbackManager;
  private projectPath: string;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化回滚管理器
    process.chdir(projectPath);
    this.rollbackManager = new RollbackManager();
  }

  /**
   * 获取可回滚版本列表 - 对应 CLI 的 rollback-list
   * 别名方法，与 IPC 处理器兼容
   */
  async getRollbackVersions(): Promise<any> {
    return this.listRollbackVersions();
  }

  /**
   * 获取可回滚版本列表 - 对应 CLI 的 rollback-list
   */
  async listRollbackVersions(options: {
    limit?: number;
  } = {}): Promise<any> {
    console.log('✅ [RollbackService] Getting rollback versions using core package:', options);

    try {
      const versions = await this.rollbackManager.getRollbackVersions(options.limit || 10);
      
      console.log('✅ [RollbackService] Got rollback versions:', versions.length);
      
      return {
        message: `找到 ${versions.length} 个可回滚版本`,
        versions,
        total: versions.length,
        limit: options.limit || 10
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error getting rollback versions:', error);
      throw new Error(`获取可回滚版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 回滚到指定版本 - 对应 CLI 的 rollback-to
   * 重载方法，支持两种调用方式
   */
  async rollbackToVersion(version: string, options?: any): Promise<any>;
  async rollbackToVersion(options: {
    version: string;
    platform?: string;
    environment?: string;
    force?: boolean;
    skipBuild?: boolean;
    skipDeploy?: boolean;
  }): Promise<any>;
  async rollbackToVersion(versionOrOptions: string | any, options?: any): Promise<any> {
    // 处理两种调用方式
    let rollbackOptions: any;
    if (typeof versionOrOptions === 'string') {
      rollbackOptions = { version: versionOrOptions, ...options };
    } else {
      rollbackOptions = versionOrOptions;
    }
    console.log('✅ [RollbackService] Rolling back to version using core package:', rollbackOptions);

    try {
      const rollbackOpts: RollbackOptions = {
        platform: rollbackOptions.platform,
        environment: rollbackOptions.environment,
        force: rollbackOptions.force,
        skipBuild: rollbackOptions.skipBuild,
        skipDeploy: rollbackOptions.skipDeploy
      };

      const rollbackRecord = await this.rollbackManager.rollbackToVersion(
        rollbackOptions.version,
        rollbackOpts
      );
      
      console.log('✅ [RollbackService] Rollback completed:', rollbackRecord);
      
      return {
        message: `回滚到版本 ${rollbackOptions.version} 成功`,
        rollbackRecord,
        fromVersion: rollbackRecord.fromVersion,
        toVersion: rollbackRecord.toVersion,
        success: rollbackRecord.success
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error rolling back to version:', error);
      throw new Error(`回滚到版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 回滚到上一个版本 - 对应 CLI 的 rollback-last
   */
  async rollbackToLast(options: {
    platform?: string;
    environment?: string;
  } = {}): Promise<any> {
    console.log('✅ [RollbackService] Rolling back to last version using core package:', options);

    try {
      const rollbackOptions: RollbackOptions = {
        platform: options.platform,
        environment: options.environment || 'staging'
      };

      const rollbackRecord = await this.rollbackManager.rollbackToLastVersion(rollbackOptions);
      
      console.log('✅ [RollbackService] Rollback to last version completed:', rollbackRecord);
      
      return {
        message: `回滚到上一版本成功: ${rollbackRecord.toVersion}`,
        rollbackRecord,
        fromVersion: rollbackRecord.fromVersion,
        toVersion: rollbackRecord.toVersion
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error rolling back to last version:', error);
      throw new Error(`回滚到上一版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查回滚状态 - 对应 CLI 的 rollback-status
   */
  async getRollbackStatus(): Promise<any> {
    console.log('✅ [RollbackService] Getting rollback status using core package...');

    try {
      const status = await this.rollbackManager.getRollbackStatus();
      
      console.log('✅ [RollbackService] Got rollback status:', status);
      
      return {
        message: '回滚状态获取成功',
        status,
        currentVersion: status.currentVersion,
        availableVersions: status.availableVersions,
        canRollback: status.canRollback,
        lastRollback: status.lastRollback
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error getting rollback status:', error);
      throw new Error(`获取回滚状态失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 创建回滚点 - 对应 CLI 的 rollback-checkpoint
   */
  async createCheckpoint(name?: string): Promise<any> {
    console.log('✅ [RollbackService] Creating rollback checkpoint using core package:', name);

    try {
      const checkpoint = await this.rollbackManager.createRollbackCheckpoint(name);
      
      console.log('✅ [RollbackService] Checkpoint created:', checkpoint);
      
      return {
        message: `回滚点创建成功: ${checkpoint.checkpointName}`,
        checkpoint,
        checkpointName: checkpoint.checkpointName,
        version: checkpoint.version,
        tag: checkpoint.tag
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error creating checkpoint:', error);
      throw new Error(`创建回滚点失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取回滚历史记录
   */
  async getRollbackHistory(options: {
    limit?: number;
  } = {}): Promise<any> {
    console.log('✅ [RollbackService] Getting rollback history using core package:', options);

    try {
      const history = await this.rollbackManager.getRollbackHistory(options.limit || 20);
      
      console.log('✅ [RollbackService] Got rollback history:', history.length, 'records');
      
      return {
        message: `获取到 ${history.length} 条回滚历史记录`,
        history,
        total: history.length,
        limit: options.limit || 20
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error getting rollback history:', error);
      throw new Error(`获取回滚历史失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取支持的环境列表
   */
  getSupportedEnvironments(): string[] {
    return ['staging', 'production'];
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return ['web-mobile', 'android', 'ios', 'windows', 'mac'];
  }

  /**
   * 检查环境是否支持
   */
  isEnvironmentSupported(environment: string): boolean {
    return this.getSupportedEnvironments().includes(environment);
  }

  /**
   * 检查平台是否支持
   */
  isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }

  /**
   * 回滚到上一个版本 - 对应 CLI 的 rollback-last
   */
  async rollbackToLastVersion(options?: any): Promise<any> {
    console.log('✅ [RollbackService] Rolling back to last version using core package:', options);

    try {
      const result = await this.rollbackManager.rollbackToLastVersion(options || {});

      console.log('✅ [RollbackService] Rollback to last version completed:', result);

      return {
        message: `回滚到上一版本成功`,
        rollbackRecord: result,
        fromVersion: result.fromVersion,
        toVersion: result.toVersion,
        success: result.success
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error rolling back to last version:', error);
      throw new Error(`回滚到上一版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证回滚可行性 - 对应 CLI 的 rollback-validate
   */
  async validateRollback(version: string): Promise<any> {
    console.log('✅ [RollbackService] Validating rollback using core package:', version);

    try {
      const validation = await this.rollbackManager.validateRollback(version);

      console.log('✅ [RollbackService] Rollback validation completed:', validation);

      return {
        message: validation.valid ? '回滚验证通过' : '回滚验证失败',
        validation,
        valid: validation.valid,
        issues: validation.issues,
        warnings: validation.warnings
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error validating rollback:', error);
      throw new Error(`回滚验证失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 创建回滚检查点 - 别名方法，与 IPC 处理器兼容
   */
  async createRollbackCheckpoint(name?: string): Promise<any> {
    return this.createCheckpoint(name);
  }
}
