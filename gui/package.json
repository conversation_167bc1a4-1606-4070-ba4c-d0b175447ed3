{"name": "@version-craft/gui", "version": "1.0.0", "description": "Version-Craft 的桌面 GUI 应用", "main": "dist/main/main/index.js", "author": "Version-Craft Team", "license": "MIT", "private": true, "scripts": {"start": "concurrently \"electron .\"", "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "tsc -p tsconfig.main.json && electron .", "dev:renderer": "vite", "build": "npm run build:renderer && npm run build:main", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vue-tsc --noEmit && vite build", "build:renderer:fast": "vite build", "type-check": "vue-tsc --noEmit", "type-check:watch": "vue-tsc --noEmit --watch", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "clean": "rimraf dist release"}, "homepage": "./", "dependencies": {"@version-craft/core": "file:../packages/core", "chokidar": "^3.5.3", "electron": "^25.9.0", "electron-store": "^8.1.0", "fs-extra": "^11.1.1", "semver": "^7.5.4", "simple-git": "^3.20.0"}, "devDependencies": {"@heroicons/vue": "^2.0.18", "@types/node": "^20.8.0", "@vitejs/plugin-vue": "^4.4.0", "@vue/tsconfig": "^0.8.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "date-fns": "^2.30.0", "electron-builder": "^24.6.4", "glob": "^11.0.3", "minimatch": "^10.0.3", "pinia": "^2.1.7", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-checker": "^0.10.3", "vue": "^3.3.8", "vue-router": "^4.2.5", "vue-tsc": "^3.0.6"}, "build": {"appId": "com.versioncraft.gui", "productName": "Version-Craft GUI", "directories": {"output": "release"}, "files": ["dist/**/*", "package.json"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}}}