# 通知系统优化测试指南

## 修改内容总结

### 1. 控制台输出优化
- **文件**: `gui/src/shared/utils/Logger.ts`
  - 新增 `outputToTerminal()` 方法，强制输出到主进程终端
  - 增强文件输出功能，通过IPC发送到主进程

- **文件**: `gui/src/shared/api/VersionCraftAPIClient.ts`
  - 增强错误日志输出，同时输出到控制台和终端
  - 添加详细的环境检查信息

- **文件**: `gui/src/shared/constants/ipc-commands.ts`
  - 新增 `WRITE_TO_TERMINAL` IPC命令

### 2. 提示框尺寸优化
- **文件**: `gui/src/renderer/components/NotificationContainer.vue`
  - 增加通知容器最大宽度至450px，最小宽度至350px
  - 优化文本换行和布局，确保长文本正确显示
  - 添加响应式设计，适配不同屏幕尺寸
  - 改进Toast容器尺寸和布局

- **文件**: `gui/src/renderer/components/Notification.vue`
  - 优化单个通知组件的尺寸控制
  - 添加响应式样式
  - 改进消息文本布局

### 3. 系统错误对话框
- **新文件**: `gui/src/renderer/components/SystemErrorDialog.vue`
  - 专门用于显示系统级错误的对话框组件
  - 支持详细信息展示、建议操作、重试等功能
  - 响应式设计，适配移动端

- **文件**: `gui/src/shared/services/ErrorHandler.ts`
  - 为"Electron API 不可用"错误使用专门的对话框
  - 通过事件系统触发系统错误对话框

- **文件**: `gui/src/renderer/App.vue`
  - 集成SystemErrorDialog组件
  - 添加系统错误事件监听和处理逻辑

## 测试步骤

### 1. 测试控制台输出
1. 启动应用程序
2. 尝试选择项目文件夹
3. 如果出现"Electron API 不可用"错误，检查：
   - 浏览器开发者工具控制台是否有详细错误信息
   - 主进程终端是否有日志输出

### 2. 测试提示框尺寸
1. 触发各种类型的通知（成功、错误、警告、信息）
2. 测试长文本消息的显示效果
3. 在不同屏幕尺寸下测试响应式效果：
   - 桌面端（>640px）
   - 平板端（640px以下）
   - 移动端（480px以下）

### 3. 测试系统错误对话框
1. 模拟"Electron API 不可用"错误
2. 检查是否显示专门的系统错误对话框
3. 测试对话框的功能：
   - 显示/隐藏详细信息
   - 重试按钮
   - 重新加载按钮
   - 关闭按钮

## 预期效果

### 控制台输出
- 错误信息同时显示在浏览器控制台和主进程终端
- 包含详细的环境检查信息
- 便于开发者调试和排查问题

### 提示框显示
- 通知框有合适的尺寸，不会截断文本
- 长文本自动换行显示
- 在不同屏幕尺寸下都能正常显示
- 布局美观，用户体验良好

### 系统错误处理
- "Electron API 不可用"错误显示专门的对话框
- 提供详细的错误信息和解决建议
- 支持重试和重新加载操作
- 对话框尺寸适中，内容完整显示

## 注意事项

1. **主进程处理器**: 需要在主进程中添加 `logger:write-to-terminal` IPC处理器
2. **样式兼容性**: 确保Tailwind CSS类名正确应用
3. **事件清理**: 组件卸载时正确清理事件监听器
4. **错误边界**: 确保错误处理不会导致应用崩溃

## 后续优化建议

1. 添加日志文件持久化存储
2. 实现错误统计和分析功能
3. 添加用户反馈收集机制
4. 优化错误恢复策略
