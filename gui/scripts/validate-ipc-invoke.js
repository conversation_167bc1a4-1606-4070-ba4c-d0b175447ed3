#!/usr/bin/env node

/**
 * 精准的 IPC invoke 调用验证脚本
 * 只查找 invoke( 开头的调用，避免误报 CSS 类名等
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { minimatch } = require('minimatch');

// 系统级事件白名单（不需要替换为常量的事件）
const SYSTEM_EVENTS_WHITELIST = [
  // Electron 系统事件
  'ready', 'ready-to-show', 'close', 'closed', 'activate', 'window-all-closed',
  'before-quit', 'will-quit', 'quit', 'open-file', 'open-url',
  'focus', 'blur', 'show', 'hide', 'minimize', 'maximize', 'restore',

  // Node.js 系统事件
  'uncaughtException', 'unhandledRejection', 'exit', 'SIGINT', 'SIGTERM',

  // DOM 事件
  'click', 'change', 'input', 'submit', 'load', 'error', 'resize',

  // Vue 事件
  'mounted', 'updated', 'destroyed', 'created'
];

/**
 * 扫描文件中的 invoke 调用和事件监听
 */
function scanInvokeCallsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];

    // 查找所有 invoke 调用模式
    const invokePatterns = [
      // window.electronAPI.invoke('command', ...)
      /window\.electronAPI\.invoke\s*\(\s*(['"][^'"]+['"])/g,

      // electronAPI.invoke('command', ...)
      /electronAPI\.invoke\s*\(\s*(['"][^'"]+['"])/g,

      // this.invoke('command', ...)
      /this\.invoke\s*\(\s*(['"][^'"]+['"])/g,

      // .invoke('command', ...)
      /\.invoke\s*\(\s*(['"][^'"]+['"])/g,

      // ipcRenderer.invoke('command', ...)
      /ipcRenderer\.invoke\s*\(\s*(['"][^'"]+['"])/g
    ];

    // 查找所有事件监听模式
    const eventPatterns = [
      // .on('event', callback)
      /\.on\s*\(\s*(['"][^'"]+['"])/g,

      // .off('event', callback)
      /\.off\s*\(\s*(['"][^'"]+['"])/g,

      // window.electronAPI.on('event', callback)
      /window\.electronAPI\.on\s*\(\s*(['"][^'"]+['"])/g,

      // electronAPI.on('event', callback)
      /electronAPI\.on\s*\(\s*(['"][^'"]+['"])/g,

      // ipcRenderer.on('event', callback)
      /ipcRenderer\.on\s*\(\s*(['"][^'"]+['"])/g,

      // sender.send('event', data)
      /sender\.send\s*\(\s*(['"][^'"]+['"])/g,

      // event.sender.send('event', data)
      /event\.sender\.send\s*\(\s*(['"][^'"]+['"])/g
    ];
    
    // 处理 invoke 调用
    invokePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const commandString = match[1]; // 包含引号的命令字符串
        const command = commandString.slice(1, -1); // 移除引号

        // 检查是否使用了常量
        if (!commandString.includes('IPC_COMMANDS')) {
          // 计算行号
          const beforeMatch = content.substring(0, match.index);
          const lineNumber = beforeMatch.split('\n').length;

          // 获取完整的调用上下文
          const lineStart = content.lastIndexOf('\n', match.index) + 1;
          const lineEnd = content.indexOf('\n', match.index + match[0].length);
          const fullLine = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd).trim();

          // 跳过注释中的代码
          if (fullLine.trim().startsWith('//')) {
            return;
          }

          issues.push({
            file: filePath,
            line: lineNumber,
            command: command,
            commandString: commandString,
            context: match[0],
            fullLine: fullLine,
            type: 'hardcoded-invoke'
          });
        }
      }
    });

    // 处理事件监听调用
    eventPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const eventString = match[1]; // 包含引号的事件字符串
        const event = eventString.slice(1, -1); // 移除引号

        // 检查是否使用了常量，并排除系统级事件
        if (!eventString.includes('IPC_COMMANDS') && !SYSTEM_EVENTS_WHITELIST.includes(event)) {
          // 计算行号
          const beforeMatch = content.substring(0, match.index);
          const lineNumber = beforeMatch.split('\n').length;

          // 获取完整的调用上下文
          const lineStart = content.lastIndexOf('\n', match.index) + 1;
          const lineEnd = content.indexOf('\n', match.index + match[0].length);
          const fullLine = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd).trim();

          issues.push({
            file: filePath,
            line: lineNumber,
            command: event,
            commandString: eventString,
            context: match[0],
            fullLine: fullLine,
            type: 'hardcoded-event'
          });
        }
      }
    });
    
    return issues;
  } catch (error) {
    console.error(`❌ 扫描文件失败 ${filePath}:`, error.message);
    return [];
  }
}

/**
 * 扫描文件中的 ipcMain.handle 注册
 */
function scanIpcHandleInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 查找 ipcMain.handle 注册
    const handlePattern = /ipcMain\.handle\s*\(\s*(['"][^'"]+['"])/g;
    
    let match;
    while ((match = handlePattern.exec(content)) !== null) {
      const commandString = match[1]; // 包含引号的命令字符串
      const command = commandString.slice(1, -1); // 移除引号
      
      // 检查是否使用了常量
      if (!commandString.includes('IPC_COMMANDS')) {
        // 计算行号
        const beforeMatch = content.substring(0, match.index);
        const lineNumber = beforeMatch.split('\n').length;
        
        // 获取完整的调用上下文
        const lineStart = content.lastIndexOf('\n', match.index) + 1;
        const lineEnd = content.indexOf('\n', match.index + match[0].length);
        const fullLine = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd).trim();
        
        issues.push({
          file: filePath,
          line: lineNumber,
          command: command,
          commandString: commandString,
          context: match[0],
          fullLine: fullLine,
          type: 'hardcoded-handle'
        });
      }
    }
    
    return issues;
  } catch (error) {
    console.error(`❌ 扫描文件失败 ${filePath}:`, error.message);
    return [];
  }
}

/**
 * 检查命令是否在常量文件中定义
 */
function checkCommandInConstants(command) {
  const constantsPath = 'src/shared/constants/ipc-commands.ts';
  
  try {
    const content = fs.readFileSync(constantsPath, 'utf8');
    
    // 检查命令是否在常量值中
    const valueRegex = new RegExp(`['"]${command.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`);
    return valueRegex.test(content);
  } catch (error) {
    console.error('❌ 读取常量文件失败:', error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始精准验证 IPC invoke 调用...\n');
  
  // 定义要扫描的文件模式
  const patterns = [
    'src/**/*.ts',
    'src/**/*.vue',
    '!src/shared/constants/ipc-commands.ts', // 排除常量定义文件
    '!node_modules/**',
    '!dist/**',
    '!scripts/**'
  ];
  
  let totalFiles = 0;
  let filesWithIssues = 0;
  let totalIssues = 0;
  const allIssues = [];
  
  console.log('🔍 扫描 invoke 调用、事件监听和 ipcMain.handle 注册...');
  
  // 扫描每个文件
  patterns.forEach(pattern => {
    if (pattern.startsWith('!')) return; // 跳过排除模式
    
    const files = glob.sync(pattern, { cwd: process.cwd() });
    
    files.forEach(file => {
      // 检查是否被排除
      const isExcluded = patterns.some(p => 
        p.startsWith('!') && minimatch(file, p.substring(1))
      );
      
      if (!isExcluded) {
        totalFiles++;
        
        // 扫描 invoke 调用和事件监听
        const invokeIssues = scanInvokeCallsInFile(file);

        // 扫描 ipcMain.handle 注册
        const handleIssues = scanIpcHandleInFile(file);
        
        const allFileIssues = [...invokeIssues, ...handleIssues];
        
        if (allFileIssues.length > 0) {
          filesWithIssues++;
          totalIssues += allFileIssues.length;
          allIssues.push(...allFileIssues);
        }
      }
    });
  });
  
  // 输出结果
  console.log(`\n📊 扫描结果:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   有问题的文件数: ${filesWithIssues}`);
  console.log(`   总问题数: ${totalIssues}`);
  
  if (allIssues.length > 0) {
    console.log('\n🔴 发现硬编码的 IPC 调用和事件监听:');
    
    // 按文件分组显示问题
    const issuesByFile = {};
    allIssues.forEach(issue => {
      if (!issuesByFile[issue.file]) {
        issuesByFile[issue.file] = [];
      }
      issuesByFile[issue.file].push(issue);
    });
    
    Object.entries(issuesByFile).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}:`);
      issues.forEach(issue => {
        let typeLabel;
        if (issue.type === 'hardcoded-handle') {
          typeLabel = '🔴 ipcMain.handle';
        } else if (issue.type === 'hardcoded-event') {
          typeLabel = '🟡 事件监听';
        } else {
          typeLabel = '🔴 invoke调用';
        }

        const inConstants = checkCommandInConstants(issue.command);
        const constantStatus = inConstants ? '✅ 已在常量中定义' : '❌ 未在常量中定义';
        
        console.log(`   ${typeLabel} 第${issue.line}行: ${issue.commandString}`);
        console.log(`      命令: ${issue.command}`);
        console.log(`      状态: ${constantStatus}`);
        console.log(`      上下文: ${issue.context}`);
        console.log(`      完整行: ${issue.fullLine}`);
        console.log('');
      });
    });
    
    console.log('\n💡 修复建议:');
    console.log('   1. 将硬编码的命令字符串替换为 IPC_COMMANDS 常量');
    console.log('   2. 确保所有命令都在 src/shared/constants/ipc-commands.ts 中定义');
    console.log('   3. 添加相应的 import 语句：import { IPC_COMMANDS } from "..."');
    
    // 统计未定义的命令
    const undefinedCommands = allIssues
      .filter(issue => !checkCommandInConstants(issue.command))
      .map(issue => issue.command);
    
    if (undefinedCommands.length > 0) {
      console.log('\n⚠️  以下命令未在常量文件中定义:');
      [...new Set(undefinedCommands)].forEach(command => {
        console.log(`   - ${command}`);
      });
    }
    
    process.exit(1); // 有问题时退出码为 1
  } else {
    console.log('\n🎉 验证通过！所有 IPC 调用都已使用常量。');
    process.exit(0);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { scanInvokeCallsInFile, scanIpcHandleInFile, checkCommandInConstants };
