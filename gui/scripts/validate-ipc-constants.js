#!/usr/bin/env node

/**
 * IPC 常量使用验证脚本
 * 全局搜索验证确保所有的接口已使用常量替代，没有硬编码字符串
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { minimatch } = require('minimatch');

// 定义所有可能的 IPC 命令模式
const IPC_PATTERNS = [
  // 新格式：namespace:action
  /['"]([a-z-]+:[a-z-]+)['"]/, 
  
  // 旧格式：action-namespace 或 namespace-action
  /['"]([a-z-]+-[a-z-]+)['"]/, 
  
  // 项目管理格式
  /['"]((select|get|set)-[a-z-]+(-[a-z-]+)*)['"]/, 
  
  // 热更新格式
  /['"]hotupdate-[a-z-]+(-[a-z-]+)*['"]/, 
  
  // 构建格式
  /['"]((start|cancel|build)-[a-z-]+)['"]/, 
  
  // 版本格式
  /['"]((get|bump|rollback)-[a-z-]+(-[a-z-]+)*)['"]/, 
  
  // 系统格式
  /['"]((open|show|quit)-[a-z-]+(-[a-z-]+)*)['"]/, 
  
  // 事件格式
  /['"]([a-z-]+-progress|[a-z-]+-complete|[a-z-]+-changed)['"]/, 
];

// 白名单：允许的硬编码字符串（非 IPC 命令）
const WHITELIST = [
  // 文件路径和配置
  'version-craft.config.json',
  'package.json',
  'tsconfig.json',
  'vite.config.ts',
  
  // CSS 类名和样式
  'text-center',
  'flex-col',
  'space-y-4',
  'bg-blue-500',
  'hover:bg-blue-600',
  
  // HTML 属性
  'data-testid',
  'aria-label',
  'role',
  
  // 其他合法字符串
  'development',
  'production',
  'renderer',
  'main',
  'preload',
  
  // 日志和错误消息中的关键词
  'error-message',
  'success-message',
  'info-message',
  
  // Vue 相关
  'v-model',
  'v-if',
  'v-for',
  
  // 已知的非 IPC 字符串
  'openDirectory',
  'showOpenDialog',
  'showSaveDialog'
];

/**
 * 检查字符串是否在白名单中
 */
function isWhitelisted(str) {
  return WHITELIST.some(pattern => str.includes(pattern));
}

/**
 * 检查字符串是否可能是 IPC 命令
 */
function isPotentialIPCCommand(str) {
  // 移除引号
  const cleanStr = str.replace(/['"]/g, '');
  
  // 检查是否匹配任何 IPC 模式
  return IPC_PATTERNS.some(pattern => pattern.test(`'${cleanStr}'`));
}

/**
 * 扫描文件中的硬编码字符串
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 查找所有字符串字面量
    const stringMatches = content.match(/(['"])[^'"]*\1/g) || [];
    
    stringMatches.forEach((match, index) => {
      const cleanMatch = match.slice(1, -1); // 移除引号
      
      // 跳过空字符串和白名单
      if (!cleanMatch || isWhitelisted(cleanMatch)) {
        return;
      }
      
      // 检查是否可能是 IPC 命令
      if (isPotentialIPCCommand(match)) {
        // 查找在文件中的行号
        const beforeMatch = content.substring(0, content.indexOf(match));
        const lineNumber = beforeMatch.split('\n').length;
        
        issues.push({
          file: filePath,
          line: lineNumber,
          match: match,
          type: 'potential-ipc-command'
        });
      }
    });
    
    // 特别检查 ipcMain.handle 和 invoke 调用
    const ipcHandleMatches = content.match(/ipcMain\.handle\s*\(\s*(['"][^'"]+['"])/g) || [];
    const invokeMatches = content.match(/\.invoke\s*\(\s*(['"][^'"]+['"])/g) || [];
    
    [...ipcHandleMatches, ...invokeMatches].forEach(match => {
      const stringMatch = match.match(/(['"][^'"]+['"])/);
      if (stringMatch && !stringMatch[0].includes('IPC_COMMANDS')) {
        const beforeMatch = content.substring(0, content.indexOf(match));
        const lineNumber = beforeMatch.split('\n').length;
        
        issues.push({
          file: filePath,
          line: lineNumber,
          match: stringMatch[0],
          type: 'ipc-hardcode',
          context: match
        });
      }
    });
    
    return issues;
  } catch (error) {
    console.error(`❌ 扫描文件失败 ${filePath}:`, error.message);
    return [];
  }
}

/**
 * 验证常量文件的完整性
 */
function validateConstants() {
  const constantsPath = 'src/shared/constants/ipc-commands.ts';
  
  try {
    const content = fs.readFileSync(constantsPath, 'utf8');
    const issues = [];
    
    // 检查是否有重复的值
    const values = [];
    const valueMatches = content.match(/:\s*['"][^'"]+['"]/g) || [];
    
    valueMatches.forEach(match => {
      const value = match.match(/['"]([^'"]+)['"]/)[1];
      if (values.includes(value)) {
        issues.push({
          type: 'duplicate-value',
          value: value,
          message: `重复的常量值: ${value}`
        });
      } else {
        values.push(value);
      }
    });
    
    // 检查命名规范
    const constantMatches = content.match(/([A-Z_]+):\s*['"][^'"]+['"]/g) || [];
    
    constantMatches.forEach(match => {
      const [, constantName] = match.match(/([A-Z_]+):/);
      if (!/^[A-Z_]+$/.test(constantName)) {
        issues.push({
          type: 'naming-convention',
          constant: constantName,
          message: `常量命名不符合规范: ${constantName}`
        });
      }
    });
    
    return issues;
  } catch (error) {
    console.error(`❌ 验证常量文件失败:`, error.message);
    return [];
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始验证 IPC 常量使用情况...\n');
  
  // 验证常量文件
  console.log('📋 验证常量文件...');
  const constantIssues = validateConstants();
  
  if (constantIssues.length > 0) {
    console.log('⚠️  常量文件问题:');
    constantIssues.forEach(issue => {
      console.log(`   - ${issue.message}`);
    });
  } else {
    console.log('✅ 常量文件验证通过');
  }
  
  // 定义要扫描的文件模式
  const patterns = [
    'src/**/*.ts',
    'src/**/*.vue',
    '!src/shared/constants/ipc-commands.ts', // 排除常量定义文件
    '!node_modules/**',
    '!dist/**',
    '!scripts/**'
  ];
  
  let totalFiles = 0;
  let filesWithIssues = 0;
  let totalIssues = 0;
  const allIssues = [];
  
  console.log('\n🔍 扫描硬编码 IPC 命令...');
  
  // 扫描每个文件
  patterns.forEach(pattern => {
    if (pattern.startsWith('!')) return; // 跳过排除模式
    
    const files = glob.sync(pattern, { cwd: process.cwd() });
    
    files.forEach(file => {
      // 检查是否被排除
      const isExcluded = patterns.some(p => 
        p.startsWith('!') && minimatch(file, p.substring(1))
      );
      
      if (!isExcluded) {
        totalFiles++;
        const issues = scanFile(file);
        
        if (issues.length > 0) {
          filesWithIssues++;
          totalIssues += issues.length;
          allIssues.push(...issues);
        }
      }
    });
  });
  
  // 输出结果
  console.log(`\n📊 扫描结果:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   有问题的文件数: ${filesWithIssues}`);
  console.log(`   总问题数: ${totalIssues}`);
  
  if (allIssues.length > 0) {
    console.log('\n⚠️  发现的问题:');
    
    // 按文件分组显示问题
    const issuesByFile = {};
    allIssues.forEach(issue => {
      if (!issuesByFile[issue.file]) {
        issuesByFile[issue.file] = [];
      }
      issuesByFile[issue.file].push(issue);
    });
    
    Object.entries(issuesByFile).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}:`);
      issues.forEach(issue => {
        const typeLabel = issue.type === 'ipc-hardcode' ? '🔴 IPC硬编码' : '🟡 疑似IPC命令';
        console.log(`   ${typeLabel} 第${issue.line}行: ${issue.match}`);
        if (issue.context) {
          console.log(`      上下文: ${issue.context}`);
        }
      });
    });
    
    console.log('\n💡 建议:');
    console.log('   1. 将硬编码的 IPC 命令替换为 IPC_COMMANDS 常量');
    console.log('   2. 确保所有新的 IPC 命令都添加到常量文件中');
    console.log('   3. 检查是否有遗漏的 import 语句');
    
    process.exit(1); // 有问题时退出码为 1
  } else {
    console.log('\n🎉 验证通过！所有 IPC 命令都已使用常量。');
    process.exit(0);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { scanFile, validateConstants, isPotentialIPCCommand };
