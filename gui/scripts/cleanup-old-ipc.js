#!/usr/bin/env node

/**
 * 清理旧的 IPC 定义脚本
 * 从 Git 历史中提取旧的 IPC 定义，然后在整个项目中查找并替换为新的常量
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { minimatch } = require('minimatch');

// 从原始 index.ts 中提取的所有旧 IPC 定义
const OLD_IPC_MAPPINGS = {
  // 项目管理
  "'select-project-directory'": 'IPC_COMMANDS.PROJECT.SELECT_DIRECTORY',
  '"select-project-directory"': 'IPC_COMMANDS.PROJECT.SELECT_DIRECTORY',
  "'get-current-project'": 'IPC_COMMANDS.PROJECT.GET_CURRENT',
  '"get-current-project"': 'IPC_COMMANDS.PROJECT.GET_CURRENT',
  "'get-project-info'": 'IPC_COMMANDS.PROJECT.GET_INFO',
  '"get-project-info"': 'IPC_COMMANDS.PROJECT.GET_INFO',

  // 版本管理 - 旧格式
  "'get-current-version'": 'IPC_COMMANDS.VERSION.GET_CURRENT',
  '"get-current-version"': 'IPC_COMMANDS.VERSION.GET_CURRENT',
  "'get-version-history'": 'IPC_COMMANDS.VERSION.GET_HISTORY',
  '"get-version-history"': 'IPC_COMMANDS.VERSION.GET_HISTORY',
  "'bump-version'": 'IPC_COMMANDS.VERSION.BUMP',
  '"bump-version"': 'IPC_COMMANDS.VERSION.BUMP',
  "'rollback-version'": 'IPC_COMMANDS.ROLLBACK.ROLLBACK_TO',
  '"rollback-version"': 'IPC_COMMANDS.ROLLBACK.ROLLBACK_TO',

  // 构建管理 - 旧格式
  "'start-build'": 'IPC_COMMANDS.BUILD.START',
  '"start-build"': 'IPC_COMMANDS.BUILD.START',
  "'cancel-build'": 'IPC_COMMANDS.BUILD.CANCEL',
  '"cancel-build"': 'IPC_COMMANDS.BUILD.CANCEL',

  // 热更新 - 旧格式
  "'hotupdate-generate-manifest'": 'IPC_COMMANDS.HOTUPDATE.GENERATE_MANIFEST',
  '"hotupdate-generate-manifest"': 'IPC_COMMANDS.HOTUPDATE.GENERATE_MANIFEST',
  "'hotupdate-generate-patch'": 'IPC_COMMANDS.HOTUPDATE.CREATE_PATCH',
  '"hotupdate-generate-patch"': 'IPC_COMMANDS.HOTUPDATE.CREATE_PATCH',
  "'hotupdate-verify-manifest'": 'IPC_COMMANDS.HOTUPDATE.VERIFY',
  '"hotupdate-verify-manifest"': 'IPC_COMMANDS.HOTUPDATE.VERIFY',
  "'hotupdate-get-history'": 'IPC_COMMANDS.HOTUPDATE.GET_HISTORY',
  '"hotupdate-get-history"': 'IPC_COMMANDS.HOTUPDATE.GET_HISTORY',
  "'hotupdate-check-changes'": 'IPC_COMMANDS.HOTUPDATE.CHECK_CHANGES',
  '"hotupdate-check-changes"': 'IPC_COMMANDS.HOTUPDATE.CHECK_CHANGES',
  "'hotupdate-clean-cache'": 'IPC_COMMANDS.HOTUPDATE.CLEAN',
  '"hotupdate-clean-cache"': 'IPC_COMMANDS.HOTUPDATE.CLEAN',
  "'hotupdate-get-config'": 'IPC_COMMANDS.HOTUPDATE.GET_CONFIG',
  '"hotupdate-get-config"': 'IPC_COMMANDS.HOTUPDATE.GET_CONFIG',
  "'hotupdate-set-config'": 'IPC_COMMANDS.HOTUPDATE.GET_CONFIG',
  '"hotupdate-set-config"': 'IPC_COMMANDS.HOTUPDATE.GET_CONFIG',
  "'hotupdate-get-stats'": 'IPC_COMMANDS.HOTUPDATE.GET_STATS',
  '"hotupdate-get-stats"': 'IPC_COMMANDS.HOTUPDATE.GET_STATS',

  // 系统操作
  "'open-external'": 'IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL',
  '"open-external"': 'IPC_COMMANDS.SYSTEM.OPEN_EXTERNAL',
  "'show-item-in-folder'": 'IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER',
  '"show-item-in-folder"': 'IPC_COMMANDS.SYSTEM.SHOW_ITEM_IN_FOLDER',
  "'quit-app'": 'IPC_COMMANDS.SYSTEM.QUIT_APP',
  '"quit-app"': 'IPC_COMMANDS.SYSTEM.QUIT_APP',

  // 事件名称
  "'build-progress'": 'IPC_COMMANDS.EVENTS.BUILD_PROGRESS',
  '"build-progress"': 'IPC_COMMANDS.EVENTS.BUILD_PROGRESS',
  "'build-complete'": 'IPC_COMMANDS.EVENTS.BUILD_COMPLETE',
  '"build-complete"': 'IPC_COMMANDS.EVENTS.BUILD_COMPLETE',
  "'version-changed'": 'IPC_COMMANDS.EVENTS.VERSION_CHANGED',
  '"version-changed"': 'IPC_COMMANDS.EVENTS.VERSION_CHANGED'
};

// 需要添加到常量文件的缺失定义
const MISSING_CONSTANTS = {
  HOTUPDATE: {
    GET_HISTORY: 'hotupdate:get-history',
    GET_STATS: 'hotupdate:get-stats'
  }
};

// 需要添加 import 的文件
const IMPORT_STATEMENT = "import { IPC_COMMANDS } from '../constants/ipc-commands';";
const IMPORT_STATEMENT_MAIN = "import { IPC_COMMANDS } from '../../shared/constants/ipc-commands';";

/**
 * 检查文件是否需要添加 import
 */
function needsImport(content) {
  return content.includes('IPC_COMMANDS.') && !content.includes("from '../constants/ipc-commands'") && !content.includes("from '../../shared/constants/ipc-commands'");
}

/**
 * 添加 import 语句
 */
function addImport(content, filePath) {
  const lines = content.split('\n');
  let importAdded = false;
  let result = [];
  
  // 确定使用哪个 import 路径
  const isMainProcess = filePath.includes('/main/');
  const importToAdd = isMainProcess ? IMPORT_STATEMENT_MAIN : IMPORT_STATEMENT;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 在最后一个 import 语句后添加我们的 import
    if (!importAdded && line.startsWith('import ') && 
        (i === lines.length - 1 || !lines[i + 1].startsWith('import '))) {
      result.push(line);
      result.push(importToAdd);
      importAdded = true;
    } else {
      result.push(line);
    }
  }
  
  // 如果没有找到 import 语句，在文件开头添加
  if (!importAdded) {
    result.unshift(importToAdd);
    result.unshift('');
  }
  
  return result.join('\n');
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 执行替换
    for (const [oldStr, newStr] of Object.entries(OLD_IPC_MAPPINGS)) {
      if (content.includes(oldStr)) {
        content = content.replace(new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newStr);
        modified = true;
      }
    }
    
    // 如果有修改且需要添加 import
    if (modified && needsImport(content)) {
      content = addImport(content, filePath);
    }
    
    // 写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 更新常量文件，添加缺失的定义
 */
function updateConstants() {
  const constantsPath = 'src/shared/constants/ipc-commands.ts';
  
  try {
    let content = fs.readFileSync(constantsPath, 'utf8');
    let modified = false;
    
    // 添加缺失的热更新常量
    if (!content.includes('GET_HISTORY: \'hotupdate:get-history\'')) {
      content = content.replace(
        /CHECK_CHANGES: 'hotupdate:check-changes'/,
        `CHECK_CHANGES: 'hotupdate:check-changes',
  GET_HISTORY: 'hotupdate:get-history',
  GET_STATS: 'hotupdate:get-stats'`
      );
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(constantsPath, content, 'utf8');
      console.log(`✅ 已更新常量文件: ${constantsPath}`);
    }
  } catch (error) {
    console.error(`❌ 更新常量文件失败:`, error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始清理旧的 IPC 定义...\n');
  
  // 首先更新常量文件
  updateConstants();
  
  // 定义要处理的文件模式
  const patterns = [
    'src/**/*.ts',
    'src/**/*.vue',
    '!src/shared/constants/ipc-commands.ts', // 排除常量定义文件
    '!node_modules/**',
    '!dist/**',
    '!scripts/**'
  ];
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  
  // 处理每个模式
  patterns.forEach(pattern => {
    if (pattern.startsWith('!')) return; // 跳过排除模式
    
    const files = glob.sync(pattern, { cwd: process.cwd() });
    
    files.forEach(file => {
      // 检查是否被排除
      const isExcluded = patterns.some(p => 
        p.startsWith('!') && minimatch(file, p.substring(1))
      );
      
      if (!isExcluded) {
        totalFiles++;
        if (processFile(file)) {
          modifiedFiles++;
        }
      }
    });
  });
  
  console.log(`\n📊 清理完成:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   修改文件数: ${modifiedFiles}`);
  console.log(`   未修改文件数: ${totalFiles - modifiedFiles}`);
  
  if (modifiedFiles > 0) {
    console.log('\n🎉 旧 IPC 定义清理成功！');
    console.log('💡 请检查修改的文件，确保 import 路径正确。');
  } else {
    console.log('\n✨ 没有找到需要清理的旧 IPC 定义。');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { processFile, OLD_IPC_MAPPINGS };
